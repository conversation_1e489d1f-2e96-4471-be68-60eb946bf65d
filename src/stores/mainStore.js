import { defineStore } from 'pinia'
import { doGet, doPost } from '@/utils/requests'
import { h, markRaw } from 'vue'
import { NIcon } from 'naive-ui'
import { buildMenuTree, getTopLevelMenus } from '@/api/menu'
import { addDynamicRoutes } from '@/utils/routeLoader'
import {
    SpeedometerOutline,
    PersonOutline,
    BuildOutline,
    MenuOutline,
    WalletOutline,
    ChatbubbleOutline,
    StarOutline,
    HeadsetOutline,
    SearchOutline,
    SyncOutline,
    BookOutline,
    HelpCircleOutline,
    BriefcaseOutline,
    FilterOutline, SettingsSharp, CarSportOutline,CarOutline,Calculator
} from '@vicons/ionicons5'
import { FlowConnection } from '@vicons/carbon'
const iconMap = {
    'Odometer': SpeedometerOutline,
    'UserFilled': PersonOutline,
    'Tools': BuildOutline,
    'Menu': MenuOutline,
    'Money': WalletOutline,
    'Message': ChatbubbleOutline,
    'StarFilled': StarOutline,
    'Service': HeadsetOutline,
    'Search': SearchOutline,
    'Refresh': SyncOutline,
    'Reading': BookOutline,
    'HelpFilled': HelpCircleOutline,
    'Suitcase': BriefcaseOutline,
    'Filter': FilterOutline,
    'SettingsSharp': SettingsSharp,
    'CarSportOutline': CarSportOutline,
    'FlowConnection': FlowConnection,
    'CarOutline': CarOutline,
    'Calculator': Calculator
}

function renderIcon(icon) {
    return () => {
        const IconComponent = iconMap[icon] || HelpCircleOutline
        return h(NIcon, null, { default: () => h(IconComponent) })
    }
}

export { iconMap }

export const useMainStore = defineStore('main', {
    state: () => ({
        menus: [],
        user: null,
        isLoggedIn: false,
        menusLoaded: false,
        menusFetching: false,
    }),
    actions: {
        async fetchMenus() {
            this.menusFetching = true
            try {
                const response = await doGet('/system/menus') // 修改为与其他地方一致的API路径
                if (response.code === 200 && Array.isArray(response.data)) {
                    // 构建菜单树
                    this.menus = markRaw(buildMenuTree(response.data))
                    this.menusLoaded = true

                    // 将原始菜单数据存储到 localStorage，供路由守卫使用
                    localStorage.setItem('menus', JSON.stringify(response.data))

                    // 使用通用的动态路由加载工具函数
                    addDynamicRoutes(response.data)

                } else {
                    console.error('菜单数据格式不正确:', response)
                    throw new Error('Invalid menu data')
                }
            } catch (error) {
                console.error('获取菜单数据失败:', error)
                // 如果获取失败，使用默认菜单
                this.menus = this.getDefaultMenus()

                // 将默认菜单存储到 localStorage
                const defaultMenusForRoutes = [
                    {
                        id: 2,
                        parentId: 1,
                        menuLabel: '我的待办',
                        menuPath: '/tasks',
                        viewPath: 'tasks/TasksPage',
                        menuIcon: 'Odometer'
                    }
                ]
                localStorage.setItem('menus', JSON.stringify(defaultMenusForRoutes))

                // 使用通用的动态路由加载工具函数处理默认菜单
                addDynamicRoutes(defaultMenusForRoutes)
            } finally {
                this.menusFetching = false
            }
        },
        // 使用外部导入的 buildMenuTree 函数替代原来的方法
        setUser(user) {
            this.user = user
            this.isLoggedIn = !!user
            if (user) {
                localStorage.setItem('user', JSON.stringify(user))
            } else {
                localStorage.removeItem('user')
            }
        },
        async logout() {
            try {
                // 调用后端登出接口
                await doPost('/system/logout')
            } catch (error) {
                console.error('登出失败:', error)
            } finally {
                // 清除状态
                this.user = null
                this.isLoggedIn = false
                this.menusLoaded = false
                this.menus = []

                // 清除所有认证相关的本地存储
                localStorage.removeItem('access_token')
                localStorage.removeItem('isAuthenticated')
                localStorage.removeItem('user')
                localStorage.removeItem('menus')

                // 确保返回一个已解决的Promise，以便可以使用await等待此方法完成
                return Promise.resolve()
            }
        },
        async checkLoginStatus() {
            // 首先检查 access_token 是否存在
            const accessToken = localStorage.getItem('access_token')
            if (!accessToken) {
                this.logout()
                return false
            }

            // 检查本地存储的用户信息
            const storedUser = localStorage.getItem('user')
            if (storedUser) {
                try {
                    this.setUser(JSON.parse(storedUser))
                    return true
                } catch (e) {
                    console.error('解析用户信息失败:', e)
                    // 继续尝试从服务器获取
                }
            }

            try {
                const response = await doGet('/system/user')
                if (response.code === 200 && response.data) {
                    this.setUser(response.data)
                    return true
                } else {
                    console.error('获取用户信息失败: 响应不符合预期', response)
                    this.logout()
                    return false
                }
            } catch (error) {
                console.error('获取用户信息失败:', error)
                this.logout()
                return false
            }
        },
        getDefaultMenus() {
            return [
            ]
        }
    },
    getters: {
        getMenus: (state) => state.menus,
        getUser: (state) => state.user,
        isUserLoggedIn: (state) => state.isLoggedIn,
        // 新增一个 getter 来获取顶级菜单（parentId 为 1 的菜单）
        getTopLevelMenus: (state) => {
            // 从 localStorage 中获取原始菜单数据
            const storedMenus = localStorage.getItem('menus')
            if (!storedMenus) return []

            try {
                const parsedMenus = JSON.parse(storedMenus)
                // 过滤出 parentId 为 1 的菜单
                const topLevelMenus = getTopLevelMenus(parsedMenus)

                // 将过滤后的菜单转换为所需的格式
                return topLevelMenus.map(menu => ({
                    label: menu.menuLabel,
                    key: menu.menuPath ? (menu.menuPath.startsWith('/') ? menu.menuPath : `/${menu.menuPath}`) : '/',
                    icon: menu.menuIcon ? renderIcon(menu.menuIcon) : null,
                    viewPath: menu.viewPath,
                    visible: menu.visible !== false // 默认为可见，除非明确设置为 false
                }))
            } catch (error) {
                console.error('解析菜单数据失败:', error)
                return []
            }
        }
    }
})
