<template>
  <div class="stock-details-page">
    <!-- 筛选条件区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">入库日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="filterForm.dateRange === 'custom'"
              v-model:value="filterForm.customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleCustomDateChange"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">车辆品牌</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.vehicleCategory"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in vehicleCategoryOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-label">仓储状态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.stockStatus"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in stockStatusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
        <div class="filter-row">
          <div class="filter-label">仓储单位</div>
          <div class="filter-options org-selector-container">
            <n-button
              type="primary"
              ghost
              @click="showOrgSelector = true"
              style="width: 350px; justify-content: flex-start"
            >
              <template #icon>
                <n-icon><Building /></n-icon>
              </template>
              {{ selectedOrgText }}
            </n-button>
            <n-button
              v-if="filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0"
              type="error"
              ghost
              size="small"
              @click="clearOrgSelection"
              style="margin-left: 8px"
            >
              清空
            </n-button>
            <!-- 显示选中的机构标签 - 在清空按钮右侧 -->
            <div
              v-if="filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0"
              class="selected-orgs-tags"
            >
              <n-tag
                v-for="org in filterForm.invoiceOrgs"
                :key="org.id"
                type="success"
                size="small"
                closable
                @close="removeOrg(org)"
                style="margin-left: 4px"
              >
                {{ org.orgName }}
              </n-tag>
            </div>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增库存
        </n-button>

        <file-upload-button
          button-type="info"
          button-text="Excel导入"
          button-mode="standard"
          accept-formats=".xlsx,.xls"
          :max-size="10"
          template-url="/templates/vehicle-stock-template.xlsx"
          :button-style="{ fontWeight: 'bold' }"
          @success="handleImportSuccess"
          @error="handleImportError"
        />

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="请输入VIN或车辆信息"
          style="width: 220px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="filteredData"
          :loading="loading"
          :row-key="(row) => row.id"
          :max-height="tableMaxHeight"
          virtual-scroll
          virtual-scroll-x
          :scroll-x="scrollX"
          :min-row-height="48"
          :height-for-row="() => 48"
          virtual-scroll-header
          :header-height="48"
          striped
          size="medium"
        />
      </div>

      <!-- 分页组件 - 固定在表格底部 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          :item-count="pagination.itemCount"
          :show-size-picker="pagination.showSizePicker"
          :show-quick-jumper="pagination.showQuickJumper"
          size="medium"
          size-picker-option-text="条/页"
          page-size-option="每页"
          :prefix="() => `共 ${pagination.itemCount} 条`"
          :display-order="['prefix', 'pages', 'size-picker']"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <n-modal
      v-model:show="dialogVisible"
      :title="dialogTitle"
      preset="card"
      :style="
        isEditDialogMaximized
          ? { width: '90%', height: '90%' }
          : { width: '600px' }
      "
      :mask-closable="false"
      transform-origin="center"
    >
      <template #header-extra>
        <n-button quaternary circle @click="toggleEditDialogSize">
          <template #icon>
            <n-icon>
              {{
                isEditDialogMaximized ? h(ContractOutline) : h(ExpandOutline)
              }}
            </n-icon>
          </template>
        </n-button>
      </template>
      <n-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="启票批次代码" path="batchCode">
              <n-input
                v-model:value="form.batchCode"
                placeholder="请输入启票批次代码"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="订单日期" path="erpOrderDate">
              <n-date-picker
                v-model:value="form.erpOrderDate"
                type="date"
                clearable
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="启票单位" path="invoiceOrgName">
              <n-input
                v-model:value="form.invoiceOrgName"
                placeholder="请输入启票单位"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="投资主体" path="investmentEntity">
              <n-input
                v-model:value="form.investmentEntity"
                placeholder="请输入投资主体"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="车辆类别" path="vehicleCategory">
              <n-input
                v-model:value="form.vehicleCategory"
                placeholder="请输入车辆类别"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="车型系列" path="vehicleSeries">
              <n-input
                v-model:value="form.vehicleSeries"
                placeholder="请输入车型系列"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="车型代码" path="vehicleModelCode">
              <n-input
                v-model:value="form.vehicleModelCode"
                placeholder="请输入车型代码"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="车型名称" path="vehicleModelName">
              <n-input
                v-model:value="form.vehicleModelName"
                placeholder="请输入车型名称"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-grid :cols="2" :x-gap="16">
          <n-grid-item>
            <n-form-item label="VIN码" path="vin">
              <n-input v-model:value="form.vin" placeholder="请输入VIN码" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="库存成本(元)" path="startBillPrice">
              <n-input-number
                v-model:value="form.startBillPrice"
                placeholder="请输入库存成本"
                style="width: 100%"
                :precision="0"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <n-form-item label="付款备注" path="paymentRemark">
          <n-input
            v-model:value="form.paymentRemark"
            type="textarea"
            placeholder="请输入付款备注信息"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="dialogVisible = false">取消</n-button>
          <n-button type="primary" @click="handleSave">确定</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 详情弹窗 -->
    <start-bill-detail-modal
      v-model:visible="detailDialogVisible"
      :id="currentDetailId"
    />

    <!-- 业务机构选择器 -->
    <biz-org-selector
      v-model:visible="showOrgSelector"
      title="选择仓储单位"
      business-permission="can_stock_in"
      @select="handleOrgSelect"
      @cancel="handleOrgCancel"
    />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
import useStockDetailsPage from "./StockDetailsPage.js";

// 使用组合式函数获取所有页面逻辑
const {
  // 组件引用
  FileUploadButton,
  StartBillDetailModal,
  BizOrgSelector,

  // 图标
  SearchOutline,
  RefreshOutline,
  AddOutline,
  EyeOutline,
  CreateOutline,
  ContractOutline,
  ExpandOutline,
  SwapHorizontalOutline,
  CopyOutline,
  Building,

  // 响应式数据
  tableRef,
  formRef,
  loading,
  dialogVisible,
  dialogTitle,
  isEdit,
  isEditDialogMaximized,
  vehicleCategoryOptions,
  stockStatusOptions,
  filterForm,
  form,
  rules,
  stocksData,
  pagination,
  windowHeight,
  detailDialogVisible,
  currentDetailId,
  showOrgSelector,

  // 计算属性
  tableMaxHeight,
  filteredData,
  columns,
  scrollX,
  selectedOrgText,

  // 日期相关
  dateRangeOptions,

  // 业务方法
  toggleEditDialogSize,
  handleDateRangeChange,
  handleCustomDateChange,
  handleSearch,
  showAddDialog,
  handleEdit,
  handleView,
  handleSave,
  handlePageChange,
  handlePageSizeChange,
  handleImportSuccess,
  handleImportError,
  handleChangeStatus,
  refreshData,

  // 机构选择器相关方法
  handleOrgSelect,
  handleOrgCancel,
  clearOrgSelection,
  removeOrg,

  // 生命周期方法
  initialize,
  cleanup,
} = useStockDetailsPage();

// 生命周期钩子
onMounted(() => {
  initialize();
});

onUnmounted(() => {
  cleanup();
});
</script>

<style lang="scss" scoped>
@use "./StockDetailsPage.scss";
</style>