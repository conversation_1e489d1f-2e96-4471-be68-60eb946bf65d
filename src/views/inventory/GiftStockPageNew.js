import { h, ref, onMounted, markRaw } from 'vue'
import { NTag, NInputNumber, NInput, NSelect, NSpace, NButton, NIcon } from 'naive-ui'
import { giftStockApi } from '@/api/giftStock'
import { formatMoney } from '@/utils/money'
import { getDictOptions } from '@/api/dict'
import messages from '@/utils/messages'
import { CheckmarkCircleOutline, CloseCircleOutline } from '@vicons/ionicons5'
import { Edit } from '@vicons/carbon'

export function useGiftStockPageNew() {
  // 赠品类型选项 - 初始默认值，将在组件挂载时从字典中获取
  const categoryOptions = ref([
    { label: '商品', value: 'GOODS' },
    { label: '服务', value: 'SERVICES' }
  ])

  // 字典映射表
  const categoryMap = ref({
    'GOODS': '商品',
    'SERVICES': '服务'
  })

  // 单位选项
  const unitOptions = [
    { label: '个', value: '个' },
    { label: '套', value: '套' },
    { label: '份', value: '份' }
  ]

  // 默认新行数据
  const defaultNewRow = {
    name: '',
    category: 'GOODS', // 默认类型为商品
    spec: '',
    unit: '个', // 默认单位为个
    price: 0, // 单价（分）
    quantity: 0, // 库存数量
    amount: 0, // 总金额（分）
    editorName: JSON.parse(localStorage.getItem('user') || '{}').nickname || '当前用户',
    createTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
  }

  // 格式化金额显示（分转元，添加千分位分隔符）
  const formatAmount = (amount) => {
    // 将分转换为元并格式化
    return formatMoney(amount ? amount / 100 : 0)
  }

  // 比较数据是否有变化
  function hasDataChanged(originalData, currentData) {
    // 比较关键字段是否有变化
    return originalData.name !== currentData.name ||
      originalData.category !== currentData.category ||
      originalData.spec !== currentData.spec ||
      originalData.unit !== currentData.unit ||
      originalData.price !== currentData.price ||
      originalData.quantity !== currentData.quantity ||
      originalData.amount !== currentData.amount
  }

  // 事件处理函数
  function handleOrgSelect(orgId) {
    console.log('Selected organization:', orgId)
  }

  function handleAddData(newRow) {
    console.log('Added new row:', newRow)
    // 标记为编辑状态，用于列渲染
    newRow.isEditing = true
  }

  function handleEditData(row) {
    console.log('Editing row:', row)
    // 保存原始数据，用于比较是否有变化
    localStorage.setItem(`original_gift_row_${row.id}`, JSON.stringify({
      name: row.name,
      category: row.category,
      spec: row.spec,
      unit: row.unit,
      price: row.price,
      quantity: row.quantity,
      amount: row.amount
    }))
    // 标记为编辑状态，用于列渲染
    row.isEditing = true
  }

  function handleCancelEdit(row) {
    console.log('Cancelling edit for row:', row)
    // 移除编辑状态标记
    row.isEditing = false

    // 清除本地存储的原始数据
    if (row.id) {
      localStorage.removeItem(`original_gift_row_${row.id}`);
    }
  }

  function handleDeleteData(row) {
    console.log('Deleted row:', row)
  }

  // 获取字典数据
  async function fetchDictOptions() {
    try {
      const res = await getDictOptions('gift_category')
      if (res && res.code === 200 && res.data) {
        // 转换字典数据为选择器选项格式
        categoryOptions.value = res.data.map(item => ({
          label: item.option_label,
          value: item.option_value
        }))

        // 构建映射表，方便快速查找
        const map = {}
        res.data.forEach(item => {
          map[item.option_value] = item.option_label
        })
        categoryMap.value = map
      }
    } catch (error) {
      console.error('获取字典数据失败:', error)
    }
  }

  async function handleSaveData(row) {
    console.log('Saving row:', row)

    // 验证必填字段
    if (!row.name) {
      messages.error('赠品名称不能为空')
      return false
    }

    // 验证字段长度
    if (row.name.length > 50) {
      messages.error('赠品名称不能超过50个字符')
      return false
    }

    if (row.spec && row.spec.length > 50) {
      messages.error('规格型号不能超过50个字符')
      return false
    }

    try {
      // 检查是否是临时ID（以temp_开头）
      const isTemporaryId = typeof row.id === 'string' && row.id.startsWith('temp_')
      let response = null

      if (row.isNew || isTemporaryId) {
        // 创建新行数据的副本，移除id字段
        const newRowData = { ...row }

        // 删除id字段，避免在新增时传入id
        if (newRowData.id) {
          delete newRowData.id
        }

        // 调用API创建赠品库存
        response = await giftStockApi.createGiftStock(newRowData)

        if (response && response.code === 200) {
          messages.success('赠品库存创建成功')

          // 获取当前选中的机构ID
          const currentOrgId = row.stockOrgId

          // 刷新数据列表
          const params = {
            stockOrgId: currentOrgId,
            page: 1,
            size: 20
          }

          const listResponse = await giftStockApi.getGiftStockList(params)

          // 更新本地数据列表
          if (listResponse && listResponse.code === 200 && listResponse.data) {
            // 通知父组件刷新数据
            // emit('refresh-data') // 这里需要在Vue组件中处理
          }
        } else {
          throw new Error(response?.message || '创建失败')
        }
      } else {
        // 检查数据是否有变化
        const originalData = JSON.parse(localStorage.getItem(`original_gift_row_${row.id}`));

        // 如果没有原始数据或数据有变化，才发送更新请求
        if (!originalData || hasDataChanged(originalData, row)) {
          // 更新现有赠品库存
          response = await giftStockApi.updateGiftStock(row)

          if (response && response.code === 200) {
            messages.success('赠品库存更新成功')
          } else {
            throw new Error(response?.message || '更新失败')
          }
        } else {

          // 数据未变化，直接返回成功
          messages.info('数据未变化')
        }

        // 清除本地存储的原始数据
        localStorage.removeItem(`original_gift_row_${row.id}`);
      }

      // 移除编辑状态标记
      row.isEditing = false
      return true
    } catch (error) {
      console.error('Failed to save gift stock:', error)
      messages.error(error.message || '保存失败')
      return false
    }
  }

  // 创建表格列定义的函数
  function createColumns(emit) {
    return markRaw([
      {
        title: '赠品名称',
        key: 'name',
        width: 200,
        fixed: 'left', // 将赠品名称设为冻结首列
        ellipsis: {
          tooltip: true
        },
        render(row) {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            return h(NInput, {
              value: row.name,
              maxlength: 50,
              showCount: true,
              onUpdateValue(v) {
                row.name = v
              }
            })
          }

          return h('span', row.name)
        }
      },
      {
        title: '赠品类型',
        key: 'category',
        width: 100,
        render(row) {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            // 处理不同格式的category值，确保选择器能正确显示当前值
            let currentValue = row.category

            return h(NSelect, {
              value: currentValue,
              options: categoryOptions.value,
              onUpdateValue(v) {
                row.category = v
              }
            })
          }

          // 从字典映射表中获取显示值
          let displayValue = categoryMap.value[row.category]

          // 如果在字典中找不到，使用备用逻辑
          if (!displayValue) {
            const isGoods = row.category === 'GOODS' ||
              row.category === 'goods' ||
              row.category === '商品' ||
              row.category.toLowerCase() === 'goods'
            displayValue = isGoods ? '商品' : '服务'
          }

          // 根据类型设置标签颜色
          const isGoods = displayValue === '商品' ||
            row.category === 'GOODS' ||
            row.category.toLowerCase() === 'goods'

          return h(NTag, { type: isGoods ? 'success' : 'info' }, {
            default: () => displayValue
          })
        }
      },
      {
        title: '规格型号',
        key: 'spec',
        width: 150,
        ellipsis: {
          tooltip: true
        },
        render(row) {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            return h(NInput, {
              value: row.spec,
              maxlength: 50,
              showCount: true,
              onUpdateValue(v) {
                row.spec = v
              }
            })
          }

          return h('span', row.spec)
        }
      },
      {
        title: '单位',
        key: 'unit',
        width: 80,
        render(row) {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            return h(NSelect, {
              value: row.unit,
              options: unitOptions,
              onUpdateValue(v) {
                row.unit = v
              }
            })
          }

          return h('span', row.unit)
        }
      },
      {
        title: '库存数量',
        key: 'quantity',
        width: 100,
        render(row) {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            return h(NInputNumber, {
              value: row.quantity || 0,
              min: 0,
              precision: 0, // 整数
              step: 1,
              buttonPlacement: 'both',
              onUpdateValue(v) {
                row.quantity = v || 0
                // 更新总金额
                row.amount = (v || 0) * (row.price || 0)
              }
            })
          }

          return h('span', row.quantity)
        }
      },
      {
        title: '单价(元)',
        key: 'price',
        width: 120,
        render(row) {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            return h(NInputNumber, {
              value: (row.price || 0) / 100, // 分转元
              min: 0,
              precision: 2, // 保留2位小数
              step: 1,
              buttonPlacement: 'both',
              onUpdateValue(v) {
                // 元转分，并保存
                const priceInCents = Math.round((v || 0) * 100)
                row.price = priceInCents
                // 更新总金额
                row.amount = (row.quantity || 0) * priceInCents
              }
            })
          }

          // 显示为千分位格式
          return h('span', formatAmount(row.price || 0))
        }
      },
      {
        title: '总价值(元)',
        key: 'amount',
        width: 160,
        render(row) {
          // 显示为千分位格式
          return h('span', formatAmount(row.amount || 0))
        }
      },
      {
        title: '经办人',
        key: 'editorName',
        width: 100
      },
      {
        title: '更新时间',
        key: 'updateTime',
        width: 180
      },
      {
        title: '操作',
        key: 'actions',
        width: 100,
        fixed: 'right',
        render: (row) => {
          // 判断当前行是否处于编辑状态
          const isEditing = row.isEditing

          if (isEditing) {
            return h(NSpace, { align: 'center' }, {
              default: () => [
                h(NButton, {
                  quaternary: true,
                  circle: true,
                  size: 'small',
                  type: 'success',
                  onClick: async () => {
                    const result = await handleSaveData(row);
                    if (result) {
                      emit('save-data', row);
                    }
                  },
                  style: 'color: #18a058; font-size: 18px;'
                }, { default: () => h(NIcon, { component: CheckmarkCircleOutline }) }),
                h(NButton, {
                  quaternary: true,
                  circle: true,
                  size: 'small',
                  type: 'error',
                  onClick: () => {
                    handleCancelEdit(row);
                    emit('cancel-edit', row);
                  },
                  style: 'color: #d03050; font-size: 18px;'
                }, { default: () => h(NIcon, { component: CloseCircleOutline }) })
              ]
            })
          }

          return h(NSpace, { align: 'center' }, {
            default: () => [
              h(NButton, {
                quaternary: true,
                circle: true,
                size: 'small',
                onClick: () => {
                  handleEditData(row);
                  emit('edit-data', row);
                }
              }, { default: () => h(NIcon, { component: Edit }) })
              // 不显示删除按钮
            ]
          })
        }
      }
    ])
  }

  // 组件挂载时获取字典数据
  onMounted(() => {
    fetchDictOptions()
  })

  return {
    categoryOptions,
    categoryMap,
    unitOptions,
    defaultNewRow,
    formatAmount,
    hasDataChanged,
    handleOrgSelect,
    handleAddData,
    handleEditData,
    handleCancelEdit,
    handleDeleteData,
    handleSaveData,
    fetchDictOptions,
    createColumns,
    giftStockApi
  }
}
