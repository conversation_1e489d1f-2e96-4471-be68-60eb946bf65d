import { ref, reactive, computed, onMounted, onUnmounted, h } from "vue";
import messages from "@/utils/messages";
import startBillApi from "@/api/startBill";
import { NIcon, NPagination, NTag, useDialog } from "naive-ui";
import {
  SearchOutline,
  RefreshOutline,
  AddOutline,
  EyeOutline,
  CreateOutline,
  CheckmarkDoneOutline,
  CopyOutline,
} from "@vicons/ionicons5";
import { Building } from "@vicons/tabler";
import {
  dateRangeOptions,
  getDateRangeParams,
  handleDateRangeChange as handleDateChange,
  handleCustomDateChange as handleCustomDate,
} from "@/utils/dateRange";
import FileUploadButton from "@/components/FileUploadButton.vue";
import StartBillDetailModal from "@/components/inventory/StartBillDetailModal.vue";
import StartBillFormModal from "@/components/inventory/StartBillFormModal.vue";
import BizOrgSelector from "@/components/bizOrg/BizOrgSelector.vue";
import { getDictOptions } from "@/api/dict";

// 格式化数字为千分位格式，保留指定小数位数
const formatNumber = (num, decimals = 2) => {
  if (num === null || num === undefined) return "-";
  return num.toLocaleString("zh-CN", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};

export default function useStartBillPage() {
  // 初始化对话框
  const dialog = useDialog();

  // 状态变量
  const tableRef = ref(null);
  const loading = ref(false);
  const dialogVisible = ref(false);
  const isEdit = ref(false);
  const currentEditData = ref(null);
  const selectedRows = ref([]);

  // 业务机构选择器相关状态
  const showOrgSelector = ref(false);

  // 车辆类别选项 - 从字典数据动态加载
  const vehicleCategoryOptions = ref([{ label: "不限", value: null }]);

  // 筛选表单
  const filterForm = reactive({
    dateRange: null,
    customDateRange: null,
    vehicleCategory: null,
    invoiceOrgs: [], // 改为数组以支持多选
    minAmount: null,
    maxAmount: null,
    keywords: "",
  });

  // 数据列表
  const startBillData = ref([]);

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 50,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    itemCount: 0, // 总记录数
    showQuickJumper: false,
  });

  // 窗口高度响应式变量
  const windowHeight = ref(window.innerHeight);

  // 计算表格最大高度 - 用于冻结表头
  const tableMaxHeight = computed(() => {
    // 计算可用高度：视窗高度 - 页面padding - 筛选区域 - 工具栏 - 分页区域
    const pagepadding = 32; // 页面上下padding
    const filterHeight = 120; // 筛选区域高度
    const toolbarHeight = 60; // 工具栏高度
    const paginationHeight = 50; // 分页区域高度（优化后）
    const margin = 16; // 额外边距（减少）

    const calculatedHeight =
      windowHeight.value -
      pagepadding -
      filterHeight -
      toolbarHeight -
      paginationHeight -
      margin;

    // 优化高度计算，减少空白区域
    // 在大屏幕上适当限制最大高度，但允许更充分利用空间
    let maxHeight = calculatedHeight; // 默认使用计算出的高度

    // 只在数据较少时限制最大高度，确保有滚动效果
    const minScrollHeight = 400; // 最小滚动高度

    // 如果计算高度过大，适当限制以保证滚动体验
    if (calculatedHeight > 800) {
      maxHeight = Math.max(calculatedHeight * 0.85, 600); // 使用85%的高度，最少600px
    } else if (calculatedHeight > 600) {
      maxHeight = calculatedHeight; // 中等高度直接使用
    } else {
      maxHeight = Math.max(calculatedHeight, minScrollHeight); // 小屏幕保证最小高度
    }

    return Math.max(300, maxHeight);
  });

  // 窗口大小变化监听器
  const handleResize = () => {
    windowHeight.value = window.innerHeight;
  };

  // 计算属性：选中机构的显示文本
  const selectedOrgText = computed(() => {
    if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
      if (filterForm.invoiceOrgs.length === 1) {
        return filterForm.invoiceOrgs[0].orgName;
      } else {
        return `已选择 ${filterForm.invoiceOrgs.length} 个机构`;
      }
    }
    return "选择启票单位";
  });

  // 表格数据
  const filteredData = computed(() => {
    return startBillData.value || [];
  });

  // 复制文本到剪贴板
  const copyToClipboard = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        messages.success("已复制到剪贴板");
      })
      .catch((err) => {
        console.error("复制失败:", err);
        messages.error("复制失败");
      });
  };

  // 表格列配置
  const columns = [
    { type: "selection", width: 50 },
    {
      title: "VIN",
      key: "vin",
      width: 200,
      fixed: "left",
      align: "center",
      render(row) {
        return h(
          "div",
          {
            style: {
              display: "flex",
              alignItems: "center",
              cursor: "pointer",
              padding: "4px 0",
              fontFamily: "Monaco, 'Courier New', monospace", // 等宽字体
              fontSize: "13px",
            },
            onClick: () => copyToClipboard(row.vin),
            title: "点击复制VIN",
          },
          [
            h(
              "span",
              {
                style: {
                  marginRight: "5px",
                  fontFamily: "Monaco, 'Courier New', monospace", // 等宽字体
                  letterSpacing: "0.5px",
                },
              },
              row.vin
            ),
            h(
              NIcon,
              {
                size: 16,
                color: "var(--primary-color)",
                style: {
                  opacity: 0.8,
                  transition: "opacity 0.2s",
                },
                onMouseover: (e) => {
                  e.target.style.opacity = 1;
                },
                onMouseout: (e) => {
                  e.target.style.opacity = 0.8;
                },
              },
              { default: () => h(CopyOutline) }
            ),
          ]
        );
      },
    },
    { title: "启票日期", key: "erpOrderDate", width: 100 },
    {
      title: "启票单位",
      key: "invoiceOrgName",
      width: 180,
    },
    {
      title: "车辆类别",
      key: "vehicleCategory",
      width: 150,
    },
    {
      title: "车型系列",
      key: "vehicleSeries",
      width: 150,
    },
    {
      title: "启票金额(元)",
      key: "startBillPrice",
      width: 120,
      sorter: (a, b) => a.startBillPrice - b.startBillPrice,
      render(row) {
        return h(
          "span",
          { style: { fontWeight: "bold" } },
          `¥${formatNumber(row.startBillPrice, 2)}`
        );
      },
    },
  ];

  // 加载车辆品牌选项
  const loadVehicleBrandOptions = async () => {
    try {
      const res = await getDictOptions("vehicle_brand");
      if (res && res.code === 200 && res.data) {
        // 转换字典数据为选择器选项格式
        const options = res.data.map((item) => ({
          label: item.option_label,
          value: item.option_value,
        }));

        // 添加"不限"选项
        vehicleCategoryOptions.value = [
          { label: "不限", value: null },
          ...options,
        ];
      }
    } catch (error) {
      console.error("获取车辆品牌选项失败:", error);
      messages.error("获取车辆品牌选项失败");
    }
  };

  // 刷新数据
  const refreshData = async () => {
    loading.value = true;
    try {
      // 构建查询参数 - 使用 page 和 size 作为分页参数名称
      const params = {
        page: pagination.page,
        size: pagination.pageSize,
      };

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords;
      }

      // 处理日期范围
      if (filterForm.dateRange) {
        const dateRange = getDateRangeParams(
          filterForm.dateRange,
          filterForm.customDateRange
        );
        if (dateRange.startDate) params.startDate = dateRange.startDate;
        if (dateRange.endDate) params.endDate = dateRange.endDate;
      }

      // 处理车辆类别
      if (filterForm.vehicleCategory) {
        params.vehicleCategory = filterForm.vehicleCategory;
      }

      // 处理启票单位 - 支持多选，使用机构代码的逗号分隔格式
      if (filterForm.invoiceOrgs && filterForm.invoiceOrgs.length > 0) {
        // 使用机构id作为机构代码，以逗号分隔的格式传入
        params.invoice_org_code = filterForm.invoiceOrgs
          .map((org) => org.id)
          .join(",");
      }

      if (filterForm.minAmount !== null) {
        params.minStartBillPrice = filterForm.minAmount; // 直接使用元作为单位
      }

      if (filterForm.maxAmount !== null) {
        params.maxStartBillPrice = filterForm.maxAmount; // 直接使用元作为单位
      }

      // 打印查询参数，用于调试


      // 调用API获取数据
      const response = await startBillApi.getStartBillList(params);

      if (response.code === 200) {
        // 直接使用返回的数据列表
        startBillData.value = response.data.list;

        // 更新分页信息
        pagination.itemCount = response.data.total;

        // 确保当前页码与API返回的一致
        if (pagination.page !== response.data.pageNum) {
          pagination.page = response.data.pageNum;
        }

        // 打印分页信息，用于调试
        console.log("分页信息:", {
          total: response.data.total,
          pages: response.data.pages,
          pageNum: response.data.pageNum,
          navigatepageNums: response.data.navigatepageNums,
        });

        // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
        if (pagination.page > response.data.pages && response.data.pages > 0) {
          pagination.page = response.data.pages;
          refreshData();
          return;
        }
      } else {
        messages.error(response.message || "数据加载失败");
      }
    } catch (error) {
      console.error("加载数据失败:", error);
      messages.error("加载数据失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  };

  // 处理日期范围变化
  const handleDateRangeChange = (value) => {
    handleDateChange(value, filterForm, handleSearch);
  };

  // 处理自定义日期变化
  const handleCustomDateChange = (dates) => {
    handleCustomDate(dates, handleSearch);
  };

  // 处理查询
  const handleSearch = () => {
    pagination.page = 1;
    refreshData();
  };

  // 处理清除关键字
  const handleClear = () => {
    // 确保关键字已被清空
    filterForm.keywords = "";
    // 重置页码并刷新数据
    pagination.page = 1;
    refreshData();
  };

  // 显示新增对话框
  const showAddDialog = () => {
    // 先重置状态
    isEdit.value = false;
    currentEditData.value = null;
    // 然后显示弹窗
    setTimeout(() => {
      dialogVisible.value = true;
    }, 0);
  };

  // 处理编辑
  const handleEdit = async (id) => {
    try {
      loading.value = true;


      // 调用API获取详细数据
      const response = await startBillApi.getStartBillDetail(id);

      if (response.code === 200) {
        // 创建一个新对象，避免直接引用可能包含循环引用的响应数据
        const data = { ...response.data };
        isEdit.value = true;
        currentEditData.value = data;
        // 使用setTimeout确保状态更新后再显示弹窗
        setTimeout(() => {
          dialogVisible.value = true;
        }, 0);
      } else {
        messages.error(response.message || "获取启票数据失败");
      }
    } catch (error) {
      console.error("获取启票数据失败:", error);
      messages.error("获取启票数据失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  };

  // 详情弹窗
  const detailDialogVisible = ref(false);
  const currentDetailId = ref(null);

  // 处理查看
  const handleView = (id) => {
    currentDetailId.value = id;
    detailDialogVisible.value = true;
  };

  // 处理选择变化
  const handleSelectionChange = (keys) => {
    selectedRows.value = startBillData.value.filter((item) =>
      keys.includes(item.id)
    );
  };

  // 处理页码变化
  const handlePageChange = (page) => {
    pagination.page = page;
    refreshData();
  };

  // 处理每页条数变化
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    refreshData();
  };

  // 处理导入成功
  const handleImportSuccess = async (fileInfo) => {
    try {
      messages.success(`文件上传成功: ${fileInfo.fileName}`);


      // 调用车辆启票的文件解析接口
      const response = await startBillApi.importStartBill(fileInfo.fileKey);

      if (response.code === 200) {
        messages.success("文件解析成功");
        // 刷新数据列表
        refreshData();
      } else {
        messages.error(response.message || "文件解析失败");
      }
    } catch (error) {
      console.error("文件解析失败:", error);
    }
  };

  // 处理导入错误
  const handleImportError = (errorMsg) => {
    console.error(`导入失败: ${errorMsg}`);
  };

  // 处理启票完成
  const handleComplete = () => {
    if (selectedRows.value.length === 0) {
      messages.warning("请选择需要完成的启票");
      return;
    }

    // 显示确认对话框
    dialog.warning({
      title: "确认启票完成",
      content: `请确认您选中的 ${selectedRows.value.length} 台车已经入库！`,
      positiveText: "确认",
      negativeText: "取消",
      onPositiveClick: async () => {
        try {
          loading.value = true;

          // 获取选中行的ID数组
          const ids = selectedRows.value.map((row) => row.id);

          // 调用启票完成API
          const response = await startBillApi.completeStartBill(ids);

          if (response.code === 200) {
            messages.success("启票已完成");
            // 刷新数据列表
            refreshData();
          } else {
            messages.error(response.message || "启票完成操作失败");
          }
        } catch (error) {
          console.error("启票完成操作失败:", error);
          messages.error("启票完成操作失败，请稍后重试");
        } finally {
          loading.value = false;
        }
      },
    });
  };

  // 处理机构选择
  const handleOrgSelect = (orgs) => {
    if (orgs && orgs.length > 0) {
      // 多选模式，保存所有选中的机构
      filterForm.invoiceOrgs = [...orgs];
      handleSearch();
    }
  };

  // 处理机构选择取消
  const handleOrgCancel = () => {
    showOrgSelector.value = false;
  };

  // 清空机构选择
  const clearOrgSelection = () => {
    filterForm.invoiceOrgs = [];
    handleSearch();
  };

  // 移除单个机构
  const removeOrg = (orgToRemove) => {
    filterForm.invoiceOrgs = filterForm.invoiceOrgs.filter(
      (org) => org.id !== orgToRemove.id
    );
    handleSearch();
  };

  // 初始化
  const initialize = () => {
    loadVehicleBrandOptions();
    refreshData();
    // 添加窗口大小变化监听器
    window.addEventListener("resize", handleResize);
  };

  // 清理事件监听器
  const cleanup = () => {
    window.removeEventListener("resize", handleResize);
  };

  // 需要添加操作列到columns中
  columns.push({
    title: "操作",
    key: "actions",
    width: 100,
    fixed: "right",
    align: "center",
    render: (row) => {
      return h(
        "div",
        { style: { display: "flex", justifyContent: "center", gap: "12px" } },
        [
          h(
            "div",
            {
              style: {
                cursor: "pointer",
                color: "#2080f0",
                fontSize: "20px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              },
              onClick: () => handleView(row.id),
            },
            [h(NIcon, { size: 20 }, { default: () => h(EyeOutline) })]
          ),
          h(
            "div",
            {
              style: {
                cursor: "pointer",
                color: "#18a058",
                fontSize: "20px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              },
              onClick: () => handleEdit(row.id),
            },
            [h(NIcon, { size: 20 }, { default: () => h(CreateOutline) })]
          ),
        ]
      );
    },
  });

  return {
    // 组件引用
    FileUploadButton,
    StartBillDetailModal,
    StartBillFormModal,
    BizOrgSelector,

    // 图标
    SearchOutline,
    RefreshOutline,
    AddOutline,
    EyeOutline,
    CreateOutline,
    CheckmarkDoneOutline,
    CopyOutline,
    Building,

    // 响应式数据
    tableRef,
    loading,
    dialogVisible,
    isEdit,
    currentEditData,
    selectedRows,
    showOrgSelector,
    vehicleCategoryOptions,
    filterForm,
    startBillData,
    pagination,
    windowHeight,
    detailDialogVisible,
    currentDetailId,

    // 计算属性
    tableMaxHeight,
    selectedOrgText,
    filteredData,

    // 表格配置
    columns,

    // 工具函数
    copyToClipboard,
    handleResize,

    // 日期相关
    dateRangeOptions,
    getDateRangeParams,
    handleDateChange,
    handleCustomDate,

    // 业务方法
    loadVehicleBrandOptions,
    refreshData,
    handleDateRangeChange,
    handleCustomDateChange,
    handleSearch,
    handleClear,
    showAddDialog,
    handleEdit,
    handleView,
    handleSelectionChange,
    handlePageChange,
    handlePageSizeChange,
    handleImportSuccess,
    handleImportError,
    handleComplete,
    handleOrgSelect,
    handleOrgCancel,
    clearOrgSelection,
    removeOrg,

    // 生命周期方法
    initialize,
    cleanup,

    // 其他工具
    formatNumber,
    dialog,
  };
}
