<template>
  <div class="stock-details-page">
    <!-- 筛选条件区域 -->
    <n-card class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">车辆品牌</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="filterForm.vehicleCategory"
              @update:value="handleSearch"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in vehicleCategoryOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新数据
        </n-button>
        <n-button type="success" @click="showAddDialog" round>
          <template #icon>
            <n-icon><AddOutline /></n-icon>
          </template>
          新增车型
        </n-button>

        <n-button type="info" @click="openSkuSelector()" round>
          <template #icon>
            <n-icon><SearchOutline /></n-icon>
          </template>
          查询车型
        </n-button>

        <file-upload-button
          button-type="info"
          button-text="Excel导入"
          button-mode="standard"
          accept-formats=".xlsx,.xls"
          :max-size="10"
          template-url="/templates/vehicle-stock-template.xlsx"
          :button-style="{ fontWeight: 'bold' }"
          @success="handleImportSuccess"
          @error="handleImportError"
        />

        <n-input
          v-model:value="filterForm.keywords"
          placeholder="请输入品牌/车系/车型"
          style="width: 220px"
          clearable
          @keydown.enter="handleSearch"
        >
          <template #prefix>
            <n-icon><SearchOutline /></n-icon>
          </template>
        </n-input>
      </n-space>
    </n-space>

    <!-- 表格容器 -->
    <div class="table-container">
      <div class="data-table-wrapper">
        <n-data-table
          ref="tableRef"
          :columns="columns"
          :data="filteredData"
          :loading="loading"
          :row-key="(row) => row.id"
          :scroll-x="800"
          :max-height="500"
          striped
          @update:checked-row-keys="handleSelectionChange"
        />
      </div>

      <!-- 分页组件固定在表格底部 -->
      <div class="pagination-container">
        <n-pagination
          v-model:page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="pagination.pageSizes"
          :item-count="pagination.itemCount"
          :show-size-picker="pagination.showSizePicker"
          :show-quick-jumper="pagination.showQuickJumper"
          size-picker-option-text="条/页"
          page-size-option="每页"
          :prefix="() => `共 ${pagination.itemCount} 条`"
          :display-order="['prefix', 'pages', 'size-picker', 'quick-jumper']"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <sku-detail-modal
      :visible="detailDialogVisible"
      @update:visible="detailDialogVisible = $event"
      :id="currentDetailId"
    />

    <!-- 编辑弹窗 -->
    <sku-edit-modal
      :visible="editDialogVisible"
      @update:visible="editDialogVisible = $event"
      @success="refreshData"
      :id="currentEditId"
    />

    <!-- SKU选择器弹窗 -->
    <vehicle-s-k-u-selector
      :visible="selectorVisible"
      @update:visible="selectorVisible = $event"
      @select="handleSkuSelected"
      :params="selectorParams"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from "vue";
import messages from "@/utils/messages";
import { formatMoney } from "@/utils/money";
import skuApi from "@/api/sku";
import { getDictOptions } from "@/api/dict";
import { NIcon, NPagination, useDialog } from "naive-ui";
import {
  SearchOutline,
  RefreshOutline,
  AddOutline,
  EyeOutline,
  CreateOutline,
} from "@vicons/ionicons5";
import FileUploadButton from "@/components/FileUploadButton.vue";
import SkuDetailModal from "@/components/inventory/SkuDetailModal.vue";
import SkuEditModal from "@/components/inventory/SkuEditModal.vue";
import VehicleSKUSelector from "@/components/inventory/VehicleSKUSelector.vue";

// 消息提示已通过 utils/messages.js 提供

// 初始化对话框
const dialog = useDialog();
window.$dialog = dialog;

// 状态变量
const tableRef = ref(null);
const loading = ref(false);
const selectedRows = ref([]);

// 车辆类别选项 - 从字典数据获取
const vehicleCategoryOptions = ref([{ label: "不限", value: null }]);

// 筛选表单
const filterForm = reactive({
  vehicleCategory: null,
  keywords: "",
});

// 数据列表
const skuData = ref([]);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: 0, // 总记录数
  showQuickJumper: false,
});

// 表格列配置
const columns = [
  {
    type: "selection",
    width: 50,
  },
  {
    title: "品牌",
    key: "brand",
    width: 120,
    align: "center",
  },
  {
    title: "车系",
    key: "series",
    width: 180,
    align: "center",
  },
  {
    title: "配置",
    key: "configName",
    width: 180,
    align: "left",
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "启票价",
    key: "sbPrice",
    width: 120,
    align: "right",
    render: (row) => {
      return formatMoney(row.sbPrice, 2, "￥");
    },
  },
  {
    title: "颜色代码",
    key: "colorCode",
    width: 100,
    align: "center",
  },
  {
    title: "操作",
    key: "actions",
    width: 150,
    fixed: "right",
    align: "center",
    render: (row) => {
      return h(
        "div",
        { style: { display: "flex", justifyContent: "center", gap: "12px" } },
        [
          h(
            "div",
            {
              style: {
                cursor: "pointer",
                color: "#2080f0",
                fontSize: "20px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              },
              onClick: () => handleView(row.id),
            },
            [h(NIcon, { size: 20 }, { default: () => h(EyeOutline) })]
          ),
          h(
            "div",
            {
              style: {
                cursor: "pointer",
                color: "#18a058",
                fontSize: "20px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              },
              onClick: () => handleEditSku(row.id),
            },
            [h(NIcon, { size: 20 }, { default: () => h(CreateOutline) })]
          ),
        ]
      );
    },
  },
];

// 表格数据
const filteredData = computed(() => {
  return skuData.value;
});

// 加载车辆品牌选项
const loadVehicleBrandOptions = async () => {
  try {
    const res = await getDictOptions("vehicle_brand");
    if (res && res.code === 200 && res.data) {
      // 转换字典数据为选择器选项格式
      const options = res.data.map((item) => ({
        label: item.option_label,
        value: item.option_value,
      }));

      // 添加"不限"选项
      vehicleCategoryOptions.value = [
        { label: "不限", value: null },
        ...options,
      ];
    }
  } catch (error) {
    console.error("获取车辆品牌选项失败:", error);
    messages.error("获取车辆品牌选项失败");
  }
};

// 初始化
onMounted(() => {
  loadVehicleBrandOptions();
  refreshData();
});

const refreshData = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      page: pagination.page,
      size: pagination.pageSize,
    };

    // 添加筛选条件
    if (filterForm.keywords) {
      params.keywords = filterForm.keywords;
    }

    // 处理车辆类别
    if (filterForm.vehicleCategory) {
      params.brand = filterForm.vehicleCategory;
    }

    // 调用API获取数据
    const response = await skuApi.getSkuList(params);

    if (response.code === 200) {
      // 直接使用返回的数据列表
      skuData.value = response.data.list;

      // 更新分页信息
      pagination.itemCount = response.data.total;
      pagination.pageCount = response.data.pages;

      // 确保当前页码与API返回的一致
      if (pagination.page !== response.data.pageNum) {
        pagination.page = response.data.pageNum;
      }

      // 打印分页信息，用于调试
      console.log("分页信息:", {
        total: response.data.total,
        pages: response.data.pages,
        pageNum: response.data.pageNum,
        navigatepageNums: response.data.navigatepageNums,
      });

      // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
      if (pagination.page > response.data.pages && response.data.pages > 0) {
        pagination.page = response.data.pages;
        refreshData();
        return;
      }
    } else {
      messages.error(response.message || "数据加载失败");
    }
  } catch (error) {
    console.error("加载数据失败:", error);
    messages.error("加载数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 处理查询
const handleSearch = () => {
  pagination.page = 1;
  refreshData();
};

// 显示新增对话框
const showAddDialog = () => {
  // 使用新的编辑弹窗组件
  currentEditId.value = null;
  editDialogVisible.value = true;
};

// 详情弹窗
const detailDialogVisible = ref(false);
const currentDetailId = ref(null);

// 编辑弹窗
const editDialogVisible = ref(false);
const currentEditId = ref(null);

// SKU选择器弹窗
const selectorVisible = ref(false);
const selectorParams = ref({});

// 处理SKU选择
const handleSkuSelected = (sku) => {
  messages.success(`已选择SKU: ${sku.skuId}`);
};

// 打开SKU选择器
const openSkuSelector = (params = {}) => {
  selectorParams.value = params;
  selectorVisible.value = true;
};

// 处理查看
const handleView = (id) => {
  currentDetailId.value = id;
  detailDialogVisible.value = true;
};

// 处理编辑
const handleEditSku = (id) => {
  currentEditId.value = id;
  editDialogVisible.value = true;
};

// 处理选择变化
const handleSelectionChange = (keys) => {
  selectedRows.value = skuData.value.filter((item) => keys.includes(item.id));
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
  refreshData();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  refreshData();
};

// 处理导入成功
const handleImportSuccess = async (fileInfo) => {
  try {
    messages.success(`文件上传成功: ${fileInfo.fileName}`);

    // 调用车辆SKU的文件解析接口
    const response = await skuApi.importSku(fileInfo.fileKey);

    if (response.code === 200) {
      messages.success("正在处理数据，请稍后刷新数据列表");
      setTimeout(() => {
        refreshData();
      }, 10000);
    } else {
      messages.error(response.message || "文件解析失败");
    }
  } catch (error) {
    console.error("文件解析失败:", error);
    messages.error("文件解析失败，请检查文件格式是否正确");
  }
};

// 处理导入错误
const handleImportError = (errorMsg) => {
  console.error(`导入失败: ${errorMsg}`);
};
</script>

<style scoped>
.stock-details-page {
  padding: 16px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.filter-card {
  margin-bottom: 0;
  flex-shrink: 0;
}

.toolbar {
  margin-bottom: 0;
  flex-shrink: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

.amount-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-separator {
  color: #999;
}

.org-selector-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.department-selector) {
  width: 350px !important;
  min-width: 350px !important;
}

/* 自定义单选按钮组样式 */
:deep(.custom-radio-group .custom-radio-button) {
  border: none;
  background: transparent;
  transition: all 0.2s;
}

:deep(.custom-radio-group .custom-radio-button:hover) {
  color: var(--primary-color);
  background-color: rgba(24, 160, 88, 0.1);
}

:deep(.custom-radio-group .custom-radio-button--checked) {
  color: #fff !important;
  background-color: var(--primary-color) !important;
  border: none !important;
}

/* 表格容器样式 */
.table-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  background-color: #fff;
  min-height: 400px;
  max-height: calc(100vh - 200px);
}

.data-table-wrapper {
  flex: 1;
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* 表格样式优化 */
:deep(.n-data-table-th) {
  background-color: #fafafa !important;
  font-weight: 600 !important;
  color: #333 !important;
  border-bottom: 1px solid #e0e0e6 !important;
  text-align: center !important;
}

:deep(.n-data-table-th .n-data-table-th__title) {
  justify-content: center;
}

:deep(.n-data-table-td) {
  border-bottom: 1px solid #f0f0f0 !important;
}

:deep(.n-data-table-tr:hover .n-data-table-td) {
  background-color: #f8f9fa !important;
}

/* 金额列样式 */
:deep(.n-data-table-td[data-col-key="sbPrice"]) {
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-weight: 600;
  color: #d03050;
}

/* 分页组件样式 - 固定在表格底部 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 15px;
  background-color: #fafafa;
  border-top: 1px solid #e0e0e6;
  min-height: 40px;
  flex-shrink: 0;
  overflow-x: auto;
}

/* 分页组件内部样式优化 */
:deep(.pagination-container .n-pagination) {
  flex-wrap: nowrap;
  white-space: nowrap;
}

/* 弹窗样式 */
:deep(.n-card-header) {
  padding-top: 12px;
  padding-bottom: 12px;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}
</style>