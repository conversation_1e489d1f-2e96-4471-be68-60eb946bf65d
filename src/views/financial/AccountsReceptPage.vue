<template>
  <div class="accounts-recept-page">
    <!-- 筛选选项区域 -->
    <n-card title="筛选条件" class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">收款日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="dateRange === 'custom'"
              v-model:value="customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleDateRangeChange('custom')"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">收款单位</div>
          <div class="filter-options">
            <department-selector
              v-model:value="selectedOrganization"
              @update:value="handleOrganizationChange"
              width="280px"
              label="请选择收款单位"
            />
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
        <n-button type="primary" @click="showAddDialog" round>
          <template #icon>
            <n-icon><AddCircleOutline /></n-icon>
          </template>
          新增
        </n-button>
        <n-button
          type="error"
          @click="batchDelete"
          round
          :disabled="selectedRows.length === 0"
        >
          <template #icon>
            <n-icon><TrashOutline /></n-icon>
          </template>
          删除
        </n-button>
        <n-input
          v-model:value="searchParams.keywords"
          placeholder="输入车架号/业务流水号按回车键搜索"
          style="width: 300px"
          clearable
          @keydown.enter="searchData"
        >
        </n-input>
      </n-space>
    </n-space>

    <!-- 数据表格 -->
    <n-data-table
      ref="tableRef"
      :columns="columns"
      :data="receptData"
      :loading="loading"
      :pagination="pagination"
      :row-key="getRowKey"
      :bordered="false"
      :single-line="false"
      :striped="true"
      size="medium"
      @update:checked-row-keys="handleCheck"
    >
      <template #empty>
        <span>暂无数据</span>
      </template>
    </n-data-table>

    <!-- 新增收款单弹窗 -->
    <n-modal
      v-model:show="receptDialogVisible"
      :title="isEditMode ? '编辑收款单' : '新增收款单'"
      preset="card"
      :style="modalStyle"
      :mask-closable="false"
      :auto-focus="false"
    >
      <recept-form
        ref="receptFormRef"
        :initial-data="receptForm"
        :is-edit="isEditMode"
        :multiple="true"
        :initial-receivable="selectedReceivable"
        :initial-receivables="selectedReceivables"
        @update:receivable="handleReceivableUpdate"
        @update:receivables="handleReceivablesUpdate"
        @submit="saveRecept"
        @cancel="receptDialogVisible = false"
      />
    </n-modal>

    <!-- 收款单详情弹窗 -->
    <n-modal
      v-model:show="detailDialogVisible"
      title="收款单详情"
      preset="card"
      :style="modalStyle"
      :mask-closable="false"
      :auto-focus="false"
    >
      <recept-detail
        :recept-data="selectedReceptDetail"
        @close="detailDialogVisible = false"
        @print="handlePrintRecept"
      />
    </n-modal>
  </div>
</template>

  <script setup>
import { ref, reactive, onMounted, onUnmounted, h, computed } from "vue";
import {
  NButton,
  NSpace,
  NIcon,
  NDataTable,
  NCard,
  NDatePicker,
  useDialog,
  NModal,
  NRadioGroup,
  NRadioButton,
  NInput,
} from "naive-ui";
import {
  RefreshOutline,
  AddCircleOutline,
  SearchOutline,
  TrashOutline,
  PrintOutline,
} from "@vicons/ionicons5";

import {
  dateRangeOptions,
  handleDateRangeChange as handleDateChange,
} from "@/utils/dateRange";
import receptApi from "@/api/recepts";
import messages from "@/utils/messages";
import { getDictOptions } from "@/api/dict";
import DepartmentSelector from "@/components/users/DepartmentSelector.vue";
import ReceptForm from "@/components/financial/ReceptForm.vue";
import ReceptDetail from "@/components/financial/ReceptDetail.vue";

// 状态变量
const tableRef = ref(null);
const loading = ref(false);
const selectedRows = ref([]);
const receptData = ref([]);

// 新增收款单弹窗相关
const receptDialogVisible = ref(false);
const receptFormRef = ref(null);
const receptSaving = ref(false);
const isEditMode = ref(false);
const windowWidth = ref(window.innerWidth); // 当前窗口宽度
const receptForm = reactive({
  id: null,
  businessSerialNo: "", // 业务流水号
  receptAccount: null, // 收款方式
  receptAmount: 0.01, // 默认值设为0.01，单位是元
  receptTime: Date.now(), // 默认为当前时间戳
  receptOrgId: 0, // 收款单位ID
});

// 收款单详情弹窗相关
const detailDialogVisible = ref(false);
const selectedReceptDetail = ref({});

// 应收账款选择器相关
const selectedReceivable = ref(null);
const selectedReceivables = ref([]);

// 机构选择器相关
const selectedOrganization = ref(null);

// 收款方式字典选项
const receptMethodOptions = ref([]);

// 搜索参数
const searchParams = reactive({
  receptNo: "",
  receptUnit: "",
  operator: "",
  organizationId: null,
  startDate: null,
  endDate: null,
  page: 1,
  size: 20,
  keywords: "",
});

// 日期范围
const dateRange = ref(null);
const customDateRange = ref(null);

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: 0,
  onChange: (page) => {
    pagination.page = page;
    searchParams.page = page;
    fetchData();
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    searchParams.page = 1;
    searchParams.size = pageSize;
    fetchData();
  },
});

// 对话框
const dialog = useDialog();

// 计算属性：根据窗口宽度计算模态框尺寸
const modalStyle = computed(() => {
  // 当窗口宽度大于768px时，宽高为浏览器尺寸的60%
  if (windowWidth.value > 768) {
    return {
      width: "60%",
      minHeight: "600px", // 设置最小高度，确保表单有足够的显示空间
      maxWidth: "90%",
    };
  }
  // 当窗口宽度小于等于768px时，宽高为浏览器尺寸的100%
  else {
    return {
      width: "100%",
      height: "100%",
      maxWidth: "100%",
    };
  }
});

// 处理窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

// 表格列配置
const columns = [
  {
    type: "selection",
    width: 40,
    align: "center",
  },
  {
    title: "收款单号",
    key: "receptSn",
    width: 110,
    align: "left",
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "收款时间",
    key: "receptTime",
    width: 110,
    align: "center",
    render(row) {
      if (!row.receptTime) return "未设置";
      // 显示完整日期时间
      return row.receptTime;
    },
  },
  {
    title: "业务流水号",
    key: "bizNo",
    width: 110,
    align: "center",
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "收款方式",
    key: "receptMethod",
    width: 150,
    align: "center",
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      // 从字典选项中查找对应的中文标签
      if (!row.receptMethod) return "-";
      const option = receptMethodOptions.value.find(
        (opt) => opt.value === row.receptMethod
      );
      return option ? option.label : row.receptMethod;
    },
  },
  {
    title: "收款金额",
    key: "receptAmount",
    width: 150,
    align: "center",
    render(row) {
      // 格式化金额显示（分转元，添加千分位分隔符）
      const amount = row.receptAmount || 0;
      const amountYuan = (amount / 100).toFixed(2);
      return `¥${Number(amountYuan).toLocaleString()}`;
    },
  },
  {
    title: "收款机构",
    key: "receptOrgName",
    width: 150,
    align: "center",
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "经办人",
    key: "receptAgentName",
    width: 100,
    align: "center",
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "收款摘要",
    key: "receptSummary",
    width: 120,
    align: "left",
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "操作",
    key: "actions",
    width: 80,
    align: "center",
    fixed: "right",
    render(row) {
      return h(NSpace, { justify: "center", size: "small" }, () => [
        // 查看详情按钮（放大镜图标）
        h(
          NButton,
          {
            size: "small",
            type: "info",
            quaternary: true,
            onClick: () => viewReceptDetail(row),
            style: "color: #2080f0; padding: 4px 8px; border-radius: 4px;",
          },
          {
            default: () =>
              h(NIcon, { size: 24 }, { default: () => h(SearchOutline) }),
          }
        ),
      ]);
    },
  },
];

// 生命周期钩子
onMounted(() => {
  fetchData();
  fetchDictOptions();

  // 添加窗口大小变化监听器
  window.addEventListener("resize", handleResize);
});

// 获取字典选项
async function fetchDictOptions() {
  try {
    // 获取收款方式选项
    const res = await getDictOptions("recept_account");
    receptMethodOptions.value = (res.data || []).map((item) => ({
      label: item.option_label,
      value: item.option_value,
    }));
  } catch (error) {
    console.error("获取字典选项失败:", error);
    messages.error("获取字典选项失败");
  }
}

// 组件卸载时移除窗口大小变化监听器
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

// 方法
async function fetchData() {
  loading.value = true;
  try {
    // 构建查询参数
    const params = { ...searchParams };

    // 如果有关键字搜索，则清空特定字段，让后端统一处理关键字搜索
    if (params.keywords) {
      // 清空特定字段，避免重复查询条件
      params.receptNo = "";
      params.receptUnit = "";
      params.operator = "";
    }

    // 如果选择了机构，添加机构ID参数
    if (selectedOrganization.value) {
      params.organizationId = selectedOrganization.value.id;
    }

    const response = await receptApi.getReceptList(params);

    // 设置数据
    receptData.value = response.data.list || [];

    // 更新分页信息
    pagination.itemCount = response.data.total || 0;
    pagination.pageCount = Math.ceil(
      (response.data.total || 0) / searchParams.size
    );
  } catch (error) {
    console.error("获取收款单数据失败:", error);
    messages.error("获取收款单数据失败");
  } finally {
    loading.value = false;
  }
}

function refreshData() {
  searchParams.page = 1;
  pagination.page = 1;
  fetchData();
}

function searchData() {
  searchParams.page = 1;
  pagination.page = 1;
  fetchData();
}

function handleDateRangeChange(value) {
  handleDateChange(value, searchParams, customDateRange);
}

function handleCheck(keys) {
  selectedRows.value = keys;
}

// 处理机构选择变化
function handleOrganizationChange(org) {
  if (org) {
    searchParams.organizationId = org.id;
  } else {
    searchParams.organizationId = null;
  }
  searchData();
}

// 显示新增收款单弹窗
function showAddDialog() {
  // 重置表单数据
  receptForm.id = null;
  receptForm.businessSerialNo = "";
  receptForm.receptAccount = null;
  receptForm.receptAmount = 0.01; // 设置一个有效的初始值（单位：元）
  receptForm.receptTime = Date.now(); // 设置当前时间戳

  // 重置选中的应收账款
  selectedReceivable.value = null;
  selectedReceivables.value = [];

  // 设置为新增模式
  isEditMode.value = false;

  // 显示弹窗
  receptDialogVisible.value = true;
}

// 处理应收账款更新（单选模式）
function handleReceivableUpdate(receivable) {
  selectedReceivable.value = receivable;
  selectedReceivables.value = [];
}

// 处理应收账款多选更新
function handleReceivablesUpdate(receivables) {
  selectedReceivables.value = receivables;
  selectedReceivable.value = null;
}

// 查看收款单详情
async function viewReceptDetail(row) {
  try {
    loading.value = true;
    // 获取收款单详情
    const response = await receptApi.getReceptDetail(row.id);

    if (response && response.data) {
      // 设置选中的收款单详情
      selectedReceptDetail.value = response.data;

      // 显示详情弹窗
      detailDialogVisible.value = true;
    } else {
      messages.error("获取收款单详情失败");
    }
  } catch (error) {
    console.error("获取收款单详情失败:", error);
    messages.error("获取收款单详情失败");

    // 如果获取详情失败，使用简单的对话框显示基本信息
    // 获取收款方式的中文标签
    let receptMethodLabel = row.receptMethod || "未设置";
    const option = receptMethodOptions.value.find(
      (opt) => opt.value === row.receptMethod
    );
    if (option) {
      receptMethodLabel = option.label;
    }

    dialog.info({
      title: "收款单基本信息",
      content: `收款单号：${row.receptSn || "暂无"}\n收款金额：¥${(
        row.receptAmount / 100
      ).toFixed(2)}\n收款方式：${receptMethodLabel}\n收款时间：${
        row.receptTime || "未设置"
      }`,
      positiveText: "确定",
    });
  } finally {
    loading.value = false;
  }
}

// 处理打印收款单
function handlePrintRecept(receptData) {
  // 这里可以实现打印功能，例如使用浏览器的打印功能

  messages.info("打印功能开发中...");

  // 简单的打印实现，可以根据需要进行扩展
  const printWindow = window.open("", "_blank");
  if (printWindow) {
    // 准备收款明细HTML
    const itemsHtml = (receptData.items || [])
      .map(
        (item) =>
          `<tr>
          <td>${item.feeName || "暂无"}</td>
          <td>¥${((item.feeAmount || 0) / 100).toFixed(2)}</td>
          <td>${item.feeSummary || "暂无"}</td>
        </tr>`
      )
      .join("");

    // 构建完整的HTML
    const htmlContent = `
        <html>
          <head>
            <title>收款单打印 - ${receptData.receptSn || ""}</title>
            <style>
              body { font-family: Arial, sans-serif; padding: 20px; }
              h1 { text-align: center; }
              .info-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
              .info-table th, .info-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              .info-table th { background-color: #f2f2f2; }
              .total { text-align: right; font-weight: bold; margin-top: 20px; }
            </style>
          </head>
          <body>
            <h1>收款单</h1>
            <p><strong>收款单号：</strong>${receptData.receptSn || "暂无"}</p>
            <p><strong>收款时间：</strong>${receptData.receptTime || "暂无"}</p>
            <p><strong>收款单位：</strong>${
              receptData.receptOrgName || "暂无"
            }</p>
            <p><strong>收款方式：</strong>${
              receptData.receptMethod || "暂无"
            }</p>
            <p><strong>业务流水号：</strong>${receptData.bizNo || "暂无"}</p>
            <p><strong>收款摘要：</strong>${
              receptData.receptSummary || "暂无"
            }</p>
            <p><strong>经办人：</strong>${
              receptData.receptAgentName || "暂无"
            }</p>

            <h2>收款明细</h2>
            <table class="info-table">
              <thead>
                <tr>
                  <th>收款科目</th>
                  <th>收款金额</th>
                  <th>收款摘要</th>
                </tr>
              </thead>
              <tbody>
                ${itemsHtml}
              </tbody>
            </table>

            <div class="total">
              <p><strong>收款金额合计：</strong>¥${(
                (receptData.receptAmount || 0) / 100
              ).toFixed(2)}</p>
              <p><strong>收款金额大写：</strong>${
                receptData.receptAmountCn || "暂无"
              }</p>
            </div>
          </body>
        </html>
      `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // 等待页面加载完成后打印
    printWindow.onload = function () {
      printWindow.print();
      setTimeout(function () {
        printWindow.close();
      }, 500);
    };
  } else {
    messages.error("打印窗口被浏览器阻止，请允许弹出窗口");
  }
}

// 保存收款单
async function saveRecept(formData) {
  receptSaving.value = true;

  try {
    // 如果是编辑模式，添加ID
    if (isEditMode.value) {
      await receptApi.updateRecept(formData);
      messages.success("收款单更新成功");
    } else {
      await receptApi.createRecept(formData);
      messages.success("收款单添加成功");
    }

    receptDialogVisible.value = false;

    // 刷新数据
    refreshData();
  } catch (error) {
    console.error("保存收款单失败:", error);
    messages.error("保存收款单失败");
  } finally {
    receptSaving.value = false;
  }
}

// 批量删除功能
function batchDelete() {
  if (selectedRows.value.length === 0) {
    messages.warning("请先选择要删除的记录");
    return;
  }

  dialog.warning({
    title: "警告",
    content: `确定要删除选中的 ${selectedRows.value.length} 条记录吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const ids = selectedRows.value;
        await receptApi.batchDeleteRecepts(ids);
        messages.success("删除成功");
        refreshData();
      } catch (error) {
        console.error("删除失败:", error);
        messages.error("删除失败");
      }
    },
  });
}

// 批量删除功能已经足够，单个删除功能暂时不需要

// 获取行的唯一标识
function getRowKey(row) {
  // 确保每行都有一个唯一的key
  if (row.id) {
    return row.id;
  }

  // 如果没有id，但有repcetSn，使用repcetSn作为key
  if (row.repcetSn) {
    return `recept-${row.repcetSn}`;
  }

  // 如果以上都没有，生成一个随机key
  return `row-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}
</script>

  <style scoped>
.accounts-recept-page {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.toolbar {
  margin-bottom: 0;
}

.filter-card {
  margin-bottom: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

/* 自定义单选按钮组样式 */
:deep(.custom-radio-group .custom-radio-button) {
  border: none;
  background: transparent;
  transition: all 0.2s;
}

:deep(.custom-radio-group .custom-radio-button:hover) {
  color: var(--primary-color);
  background-color: rgba(24, 160, 88, 0.1);
}

:deep(.custom-radio-group .custom-radio-button--checked) {
  color: #fff !important;
  background-color: var(--primary-color) !important;
  border: none !important;
}

:deep(.n-data-table) {
  margin-top: 0;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  flex: 1;
}

:deep(.n-data-table-th) {
  background-color: #f9f9f9 !important;
  font-weight: 600;
  padding: 12px 8px;
}

:deep(.n-data-table-td) {
  padding: 10px 8px;
}

:deep(.n-button) {
  margin-right: 4px;
}

/* 表格行样式 */
:deep(.n-data-table-tr) {
  transition: background-color 0.3s;
}

:deep(.n-data-table-tr:hover) {
  background-color: #f5f7fa;
}

/* 确保表头和内容对齐 */
:deep(.n-data-table-th .n-data-table-th__title) {
  display: flex;
  justify-content: center;
}

:deep(.n-data-table-th[data-col-key="receptNo"] .n-data-table-th__title) {
  justify-content: flex-start;
}

:deep(.n-data-table-th[data-col-key="receptAmount"] .n-data-table-th__title) {
  justify-content: center;
}

/* 确保所有单元格内容居中 */
:deep(.n-data-table-td .n-data-table-td__content) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 收款单号列左对齐 */
:deep(.n-data-table-td[data-col-key="receptNo"] .n-data-table-td__content) {
  justify-content: flex-start;
}

/* 金额列居中对齐 */
:deep(.n-data-table-td[data-col-key="receptAmount"] .n-data-table-td__content) {
  justify-content: center;
}

/* 添加以下样式来增加图标之间的间距 */
:deep(.n-icon) {
  margin: 0 2px;
}

/* 操作列图标样式 */
:deep(.n-data-table-td[data-col-key="actions"] .n-icon) {
  font-size: 24px;
  margin: 0 4px;
  transition: transform 0.3s;
}

:deep(.n-data-table-td[data-col-key="actions"] .n-button:hover .n-icon) {
  transform: scale(1.2);
}

/* 为操作栏添加新的样式 */
:deep(.n-data-table-td[data-col-key="actions"] .n-space) {
  gap: 4px !important;
}

/* 优化按钮样式 */
:deep(.n-button.n-button--quaternary) {
  padding: 4px 12px;
  height: 32px;
  line-height: 24px;
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.n-data-table-td[data-col-key="actions"] .n-button) {
  min-width: 40px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.n-button.n-button--quaternary:hover) {
  background-color: rgba(24, 160, 88, 0.15);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

:deep(.n-button.n-button--quaternary.n-button--primary:hover) {
  background-color: rgba(32, 128, 240, 0.15);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}
</style>
