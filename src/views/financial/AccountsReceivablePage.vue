<template>
  <div class="accounts-receivable-page">
    <!-- 筛选选项区域 -->
    <n-card title="筛选条件" class="filter-card">
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-label">对账日期</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="dateRange"
              @update:value="handleDateRangeChange"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in dateRangeOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
            <n-date-picker
              v-if="dateRange === 'custom'"
              v-model:value="customDateRange"
              type="daterange"
              clearable
              class="custom-date-picker"
              @update:value="handleDateRangeChange('custom')"
            />
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-label">对账状态</div>
          <div class="filter-options">
            <n-radio-group
              v-model:value="searchParams.status"
              @update:value="searchData"
              class="custom-radio-group"
            >
              <n-radio-button
                v-for="option in statusOptions"
                :key="option.value"
                :value="option.value"
                class="custom-radio-button"
              >
                {{ option.label }}
              </n-radio-button>
            </n-radio-group>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 工具栏区域 -->
    <n-space class="toolbar" justify="space-between">
      <n-space>
        <n-button type="primary" @click="refreshData" round>
          <template #icon>
            <n-icon><RefreshOutline /></n-icon>
          </template>
          刷新
        </n-button>
        <n-button type="primary" @click="showAddDialog" round>
          <template #icon>
            <n-icon><AddCircleOutline /></n-icon>
          </template>
          新增
        </n-button>
        <n-input
          v-model:value="searchParams.keywords"
          placeholder="输入订单号/车架号/客户手机按回车键搜索"
          style="width: 300px"
          clearable
          @keydown.enter="searchData"
        >
        </n-input>
      </n-space>
    </n-space>

    <!-- 数据表格 -->
    <n-data-table
      ref="tableRef"
      :columns="columns"
      :data="receivableData"
      :loading="loading"
      :pagination="pagination"
      :row-key="getRowKey"
      :children-key="'children'"
      :expanded-row-keys="expandedKeys"
      :bordered="false"
      :single-line="false"
      :striped="true"
      size="medium"
      @update:expanded-row-keys="handleExpand"
      @update:checked-row-keys="handleCheck"
    >
      <template #empty>
        <span>暂无数据</span>
      </template>
    </n-data-table>

    <!-- 新增应收账款弹窗 -->
    <n-modal
      v-model:show="receivableDialogVisible"
      title="新增应收账款"
      preset="card"
      style="width: 500px"
      :mask-closable="false"
    >
      <n-form
        ref="receivableFormRef"
        :model="receivableForm"
        :rules="receivableRules"
        label-placement="top"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="订单编号" path="orderSn">
          <n-input
            v-model:value="receivableForm.orderSn"
            placeholder="请输入订单编号"
            :disabled="!orderSnEditable"
          />
        </n-form-item>

        <n-form-item label="应收科目" path="feeName">
          <n-select
            v-model:value="receivableForm.feeName"
            :options="subjectOptions"
            placeholder="请选择应收科目"
            @update:value="handleFeeNameChange"
            :status="receivableForm.feeName ? 'success' : undefined"
          />
        </n-form-item>

        <n-form-item label="应收对象" path="feeTarget">
          <n-select
            v-model:value="receivableForm.feeTarget"
            :options="targetOptions"
            placeholder="请选择应收对象"
            @update:value="handleFeeTargetChange"
            :status="receivableForm.feeTarget ? 'success' : undefined"
          />
        </n-form-item>

        <n-form-item label="应收金额（元）" path="feeAmount">
          <n-input-number
            v-model:value="receivableForm.feeAmount"
            :min="0.01"
            :precision="2"
            button-placement="both"
            :step="1000"
            @update:value="handleAmountChange"
            style="width: 100%"
            :default-value="0.01"
            :validator="(value) => value >= 0.01"
            clearable
          >
            <template #prefix> ¥ </template>
          </n-input-number>
        </n-form-item>

        <n-form-item label="应收金额（大写）">
          <n-input v-model:value="amountInChinese" disabled />
        </n-form-item>
      </n-form>

      <template #footer>
        <n-space justify="end">
          <n-button @click="receivableDialogVisible = false">取消</n-button>
          <n-button
            type="primary"
            @click="saveReceivable"
            :loading="receivableSaving"
            >确定</n-button
          >
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h, computed, watch } from "vue";
import {
  NButton,
  NSpace,
  NIcon,
  NDataTable,
  NCard,
  NFormItem,
  NInput,
  NSelect,
  NDatePicker,
  useDialog,
  NModal,
  NForm,
  NInputNumber,
  NRadioGroup,
  NRadioButton,
  NTag,
} from "naive-ui";
import {
  RefreshOutline,
  AddCircleOutline,
  CheckmarkCircleOutline,
  TrashOutline,
} from "@vicons/ionicons5";
import { nextTick, defineComponent } from "vue";

import {
  dateRangeOptions,
  handleDateRangeChange as handleDateChange,
} from "@/utils/dateRange";
import arbApi from "@/api/arb";
import messages from "@/utils/messages";
import { getDictOptions } from "@/api/dict";
import { convertNumberToChinese } from "@/utils/money";
import { doPut, doDelete } from "@/utils/requests";

// 状态变量
const tableRef = ref(null);
const loading = ref(false);
const selectedRows = ref([]);
const expandedKeys = ref([]);
const receivableData = ref([]);

// 新增应收账款弹窗相关
const receivableDialogVisible = ref(false);
const receivableFormRef = ref(null);
const receivableSaving = ref(false);
const orderSnEditable = ref(true); // 控制订单编号是否可编辑
const receivableForm = reactive({
  orderSn: "",
  feeName: null, // 这里存储的是应收科目的option_value，实际上是feeId
  feeTarget: null,
  feeAmount: 0.01, // 默认值设为0.01，单位是元
});

// 创建可编辑金额组件
const EditableAmount = defineComponent({
  props: {
    value: Number, // 金额，单位为分
    onUpdateValue: Function,
    disabled: Boolean,
  },
  setup(props) {
    const isEdit = ref(false);
    const inputRef = ref(null);
    // 使用计算属性来动态获取当前值，确保每次渲染时都使用最新的props.value
    const currentValue = computed(() => props.value || 0);
    // 将分转换为元的输入值
    const inputValue = ref((currentValue.value / 100).toFixed(2));

    // 当props.value变化时，更新inputValue和显示值
    watch(
      () => props.value,
      (newValue) => {
        if (!isEdit.value) {
          // 只在非编辑状态下更新，避免编辑时被覆盖
          inputValue.value = ((newValue || 0) / 100).toFixed(2);
        }
      },
      { immediate: true }
    ); // 立即执行一次

    function handleOnClick() {
      if (props.disabled) return;
      // 在进入编辑模式前，确保inputValue是最新的
      inputValue.value = (currentValue.value / 100).toFixed(2);
      isEdit.value = true;
      nextTick(() => {
        inputRef.value?.focus();
      });
    }

    // 处理输入框值变化 - 只更新本地状态，不发送请求
    function handleInputChange(v) {
      inputValue.value = v !== null ? v.toString() : "0";
    }

    // 处理失去焦点 - 验证并发送请求
    function handleBlur() {
      // 检查金额是否大于0
      const amount = parseFloat(inputValue.value);
      if (isNaN(amount) || amount <= 0) {
        // 如果金额无效，恢复原值
        inputValue.value = (currentValue.value / 100).toFixed(2);
        isEdit.value = false;
        return;
      }

      // 将元转换为分
      const amountInCents = Math.round(amount * 100);

      // 检查金额是否发生变化
      if (amountInCents !== currentValue.value) {
        // 只有金额发生变化时才调用更新函数
        props.onUpdateValue(amountInCents);
      }

      isEdit.value = false;
    }

    // 格式化显示金额的计算属性，确保每次渲染时都使用最新值
    const formattedAmount = computed(() => {
      const valueInYuan = (currentValue.value / 100).toFixed(2);
      return `¥${Number(valueInYuan).toLocaleString()}`;
    });

    return () => {
      return h(
        "div",
        {
          style: props.disabled
            ? "min-height: 22px"
            : "min-height: 22px; cursor: pointer; text-decoration: underline dotted; color: #18a058;",
          onClick: handleOnClick,
        },
        isEdit.value
          ? h(NInputNumber, {
              ref: inputRef,
              value: parseFloat(inputValue.value),
              onUpdateValue: handleInputChange, // 输入变化时只更新本地状态
              onBlur: handleBlur, // 失去焦点时才验证并发送请求
              min: 0.01,
              precision: 2,
              style: "width: 160px;",
              buttonPlacement: "both",
            })
          : formattedAmount.value // 使用计算属性的值
      );
    };
  },
});

// 应收科目选项
const subjectOptions = ref([]);
// 应收对象选项
const targetOptions = ref([]);

// 应收金额大写
const amountInChinese = computed(() => {
  if (!receivableForm.feeAmount || receivableForm.feeAmount <= 0) {
    return "零元整";
  }
  // 输入框中是元为单位，需要传递给转换函数
  return convertNumberToChinese(receivableForm.feeAmount);
});

// 搜索参数
const searchParams = reactive({
  orderSn: "",
  customerName: "",
  salesOrgName: "",
  status: "NOT_CONFIRM", // 默认选中未对账
  startDate: null,
  endDate: null,
  page: 1,
  size: 20,
  keywords: "",
});

// 日期范围
const dateRange = ref(null);
const customDateRange = ref(null);

// 应收状态选项
const statusOptions = [
  { label: "不限", value: null },
  { label: "未对账", value: "NOT_CONFIRM" },
  { label: "已对账", value: "CONFIRMED" },
];

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: 0,
  onChange: (page) => {
    pagination.page = page;
    searchParams.page = page;
    fetchData();
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    searchParams.page = 1;
    searchParams.size = pageSize;
    fetchData();
  },
});

// 对话框
const dialog = useDialog();

// 表格列配置
const columns = [
  {
    type: "selection",
    width: 40,
    align: "center",
  },
  {
    title: "订单号",
    key: "orderSn",
    width: 160,
    align: "left",
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      // 子项不显示订单号
      if (row.feeId) return "";
      return row.orderSn;
    },
  },
  {
    title: "订单日期",
    key: "orderDate",
    width: 100,
    align: "center",
    render(row) {
      // 子行不显示订单日期
      if (row.feeId) return "";

      if (!row.orderDate) return "未设置";
      // 只显示日期部分
      return row.orderDate.split(" ")[0];
    },
  },
  {
    title: "客户名称",
    key: "customerName",
    width: 100,
    align: "center",
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      // 子项不显示客户名称
      if (row.feeId) return "";
      return row.customerName;
    },
  },
  {
    title: "联系电话",
    key: "mobile",
    width: 120,
    align: "center",
    render(row) {
      // 子行不显示联系方式
      if (row.feeId) return "";

      if (!row.mobile) return "-";
      // 手机号脱敏处理
      return row.mobile.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
    },
  },
  {
    title: "销售单位",
    key: "salesOrgName",
    width: 120,
    align: "center",
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      // 子项不显示销售单位
      if (row.feeId) return "";
      return row.salesOrgName;
    },
  },
  {
    title: "销售顾问",
    key: "salesAgentName",
    width: 100,
    align: "center",
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      // 子项不显示销售顾问
      if (row.feeId) return "";
      return row.salesAgentName;
    },
  },
  {
    title: "支付方式",
    key: "paymentMethod",
    width: 100,
    align: "center",
    render(row) {
      // 如果是子项，不显示支付方式
      if (row.feeId) return "";

      const methodMap = {
        FULL: "全款",
        LOAN: "分期",
      };
      return methodMap[row.paymentMethod] || row.paymentMethod || "-";
    },
  },
  {
    title: "车架号",
    key: "vin",
    width: 120,
    align: "center",
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      // 子行不显示VIN
      if (row.feeId) return "";

      // 父行显示VIN，如果有的话
      return row.vin || "-";
    },
  },
  {
    title: "应收科目",
    key: "feeName",
    width: 120,
    align: "center",
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: "应收对象",
    key: "feeTarget",
    width: 100,
    align: "center",
  },
  {
    title: "应收金额",
    key: "feeAmount",
    width: 120,
    align: "center",
    render(row) {
      // 更新金额的处理函数
      const handleUpdateAmount = async (newAmount) => {
        // 检查金额是否与原金额相同
        if (newAmount === row.feeAmount) {
          return;
        }

        // 记录金额变化

        try {
          // 调用API更新金额
          const url = `/financial/receivable/bill`;
          await doPut(url, {
            id: row.id,
            feeAmount: newAmount, // 传入分为单位的金额
          });

          messages.success("金额更新成功");

          // 直接从服务器重新获取最新数据
          await fetchData();

          // 确保展开状态保持不变
          nextTick(() => {
            // 如果之前有展开的行，保持展开状态
            if (expandedKeys.value.length > 0) {
              const parentOrderSn = row.orderSn;
              const parentRow = receivableData.value.find(
                (item) => item.orderSn === parentOrderSn
              );
              if (parentRow && !expandedKeys.value.includes(parentRow.id)) {
                expandedKeys.value.push(parentRow.id);
              }
            }
          });
        } catch (error) {
          console.error("更新金额失败:", error);
          messages.error("更新金额失败");
        }
      };

      // 使用可编辑组件渲染
      return h(EditableAmount, {
        value: row.feeAmount || 0,
        disabled: !row.feeId || row.confirmed === true, // 只有子行且未确认的才可编辑
        onUpdateValue: handleUpdateAmount,
      });
    },
  },
  {
    title: "对账状态",
    key: "confirmed",
    width: 100,
    align: "center",
    render(row) {
      // 子行显示自己的对账状态
      if (row.feeId) {
        const confirmed = row.confirmed === true;
        return h(
          NTag,
          {
            type: confirmed ? "success" : "error",
            bordered: false,
            style: {
              padding: "2px 8px",
              fontWeight: "bold",
            },
          },
          { default: () => (confirmed ? "已对账" : "未对账") }
        );
      }

      // 父行显示所有子行的对账状态汇总
      if (row.children && row.children.length > 0) {
        const allConfirmed = row.children.every(
          (child) => child.confirmed === true
        );
        const allNotConfirmed = row.children.every(
          (child) => child.confirmed === false
        );

        let type = "warning";
        let text = "未完成";

        if (allConfirmed) {
          type = "success";
          text = "已对账";
        } else if (allNotConfirmed) {
          type = "error";
          text = "未对账";
        }

        return h(
          NTag,
          {
            type: type,
            bordered: false,
            style: {
              padding: "2px 8px",
              fontWeight: "bold",
            },
          },
          { default: () => text }
        );
      }

      return "-";
    },
  },
  {
    title: "操作",
    key: "actions",
    width: 100,
    align: "center",
    fixed: "right",
    render(row) {
      if (row.feeId) {
        // 子行根据对账状态显示不同的操作按钮
        const confirmed = row.confirmed === true;

        // 如果已对账，不显示任何按钮
        if (confirmed) {
          return null;
        }

        // 如果未对账，显示删除和确认按钮
        return h(NSpace, { justify: "center", size: "small" }, () => [
          // 删除按钮
          h(
            NButton,
            {
              size: "small",
              type: "error",
              quaternary: true,
              onClick: () => deleteReceivable(row),
              style: "color: #d03050; padding: 4px 8px; border-radius: 4px;", // 设置为红色并增加内边距
            },
            {
              default: () =>
                h(NIcon, { size: 24 }, { default: () => h(TrashOutline) }),
            }
          ),

          // 确认按钮
          h(
            NButton,
            {
              size: "small",
              type: "success",
              quaternary: true,
              onClick: () => confirmPayment(row),
              style: "color: #18a058; padding: 4px 8px; border-radius: 4px;", // 设置为绿色并增加内边距
            },
            {
              default: () =>
                h(
                  NIcon,
                  { size: 24 },
                  { default: () => h(CheckmarkCircleOutline) }
                ),
            }
          ),
        ]);
      }

      // 父行显示加号图标和确认图标（如果未全部确认）
      const buttons = [];

      // 加号图标 - 添加新项目
      buttons.push(
        h(
          NButton,
          {
            size: "large",
            type: "primary",
            quaternary: true,
            onClick: () => addNewItem(row),
            style: "color: #2080f0; padding: 4px 8px; border-radius: 4px;", // 设置为蓝色并增加内边距
          },
          {
            default: () =>
              h(NIcon, { size: 24 }, { default: () => h(AddCircleOutline) }),
          }
        )
      );

      // 检查是否所有子项都已确认
      const allConfirmed =
        row.children &&
        row.children.length > 0 &&
        row.children.every((child) => child.confirmed === true);

      // 如果不是所有子项都已确认，则显示确认图标
      if (!allConfirmed && row.children && row.children.length > 0) {
        buttons.push(
          h(
            NButton,
            {
              size: "large",
              type: "success",
              quaternary: true,
              onClick: () => confirmOrder(row),
              style: "color: #18a058; padding: 4px 8px; border-radius: 4px;", // 设置为绿色并增加内边距
            },
            {
              default: () =>
                h(
                  NIcon,
                  { size: 24 },
                  { default: () => h(CheckmarkCircleOutline) }
                ),
            }
          )
        );
      }

      return h(NSpace, { justify: "center", size: "small" }, () => buttons);
    },
  },
];

// 表单验证规则
const receivableRules = {
  orderSn: {
    required: true,
    message: "请输入订单编号",
    trigger: ["blur", "input"],
  },
  feeName: {
    required: true,
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择应收科目");
      }
      return true;
    },
    trigger: ["blur", "change", "input", "update"],
  },
  feeTarget: {
    required: true,
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择应收对象");
      }
      return true;
    },
    trigger: ["blur", "change", "input", "update"],
  },
  feeAmount: {
    required: true,
    validator: (_, value) => {
      if (value === null || value === undefined || value === "") {
        return new Error("请输入应收金额");
      }
      if (typeof value === "number" && value < 0.01) {
        return new Error("应收金额必须大于0");
      }
      return true;
    },
    trigger: ["blur", "change", "input"],
  },
};

// 生命周期钩子
onMounted(() => {
  fetchData();
  fetchDictOptions();
});

// 方法
async function fetchData() {
  loading.value = true;
  try {
    // 构建查询参数
    const params = { ...searchParams };

    // 如果有关键字搜索，则清空特定字段，让后端统一处理关键字搜索
    if (params.keywords) {
      // 清空特定字段，避免重复查询条件
      params.orderSn = "";
      params.customerName = "";
      params.salesOrgName = "";
    }

    const response = await arbApi.getArbList(params);

    // 使用arbApi中的处理方法按订单号分组
    const processedData = arbApi.processGroupedData(response);
    receivableData.value = processedData.list;

    // 更新分页信息
    pagination.itemCount = processedData.total;
    pagination.pageCount = Math.ceil(processedData.total / searchParams.size);
  } catch (error) {
    console.error("获取应收账款数据失败:", error);
    messages.error("获取应收账款数据失败");
  } finally {
    loading.value = false;
  }
}

function refreshData() {
  searchParams.page = 1;
  pagination.page = 1;
  fetchData();
}

function searchData() {
  searchParams.page = 1;
  pagination.page = 1;
  fetchData();
}

function handleDateRangeChange(value) {
  handleDateChange(value, searchParams, customDateRange);
}

function handleCheck(keys) {
  selectedRows.value = keys;
}

function handleExpand(keys) {
  expandedKeys.value = keys;
}

// 获取字典选项
async function fetchDictOptions() {
  try {
    // 获取应收科目选项
    const subjectRes = await getDictOptions("receivable_subject");
    subjectOptions.value = (subjectRes.data || []).map((item) => ({
      label: item.option_label,
      value: item.option_value,
    }));

    // 获取应收对象选项
    const targetRes = await getDictOptions("receivable_target");
    targetOptions.value = (targetRes.data || []).map((item) => ({
      label: item.option_label,
      value: item.option_value,
    }));
  } catch (error) {
    console.error("获取字典选项失败:", error);
    messages.error("获取字典选项失败");
  }
}

// 处理应收科目变化
function handleFeeNameChange(value) {
  // 当应收科目变化时，更新值
  receivableForm.feeName = value;

  // 如果选择了有效值，手动验证表单
  if (value) {
    // 延迟验证，确保表单状态已更新
    setTimeout(() => {
      receivableFormRef.value
        ?.validate(["feeName"])
        .then(() => console.log("应收科目验证通过"))
        .catch((err) => console.error("应收科目验证失败:", err));
    }, 0);
  }
}

// 处理应收对象变化
function handleFeeTargetChange(value) {
  // 当应收对象变化时，更新值
  receivableForm.feeTarget = value;

  // 如果选择了有效值，手动验证表单
  if (value) {
    // 延迟验证，确保表单状态已更新
    setTimeout(() => {
      receivableFormRef.value
        ?.validate(["feeTarget"])
        .then(() => console.log("应收对象验证通过"))
        .catch((err) => console.error("应收对象验证失败:", err));
    }, 0);
  }
}

// 处理金额变化
function handleAmountChange(value) {
  // 当金额变化时，更新值
  receivableForm.feeAmount = value;

  // 如果用户清空了输入框，设置一个默认值
  if (value === null || value === undefined || value === "") {
    // 延迟设置，避免与用户输入冲突
    setTimeout(() => {
      receivableForm.feeAmount = 0.01;
    }, 0);
  }
}

// 显示新增应收账款弹窗
function showAddDialog() {
  receivableForm.orderSn = "";
  receivableForm.feeName = null; // 应收科目ID
  receivableForm.feeTarget = null; // 应收对象
  receivableForm.feeAmount = 0.01; // 设置一个有效的初始值（单位：元）
  orderSnEditable.value = true; // 允许编辑订单编号
  receivableDialogVisible.value = true;
}

// 显示新增应收账款弹窗（带订单信息）
function showAddReceivableDialog(row) {
  receivableForm.orderSn = row.orderSn;
  receivableForm.feeName = null; // 应收科目ID
  receivableForm.feeTarget = null; // 应收对象
  receivableForm.feeAmount = 0.01; // 设置一个有效的初始值（单位：元）
  orderSnEditable.value = false; // 禁止编辑订单编号
  receivableDialogVisible.value = true;
}

// 保存应收账款
function saveReceivable() {
  // 确保金额字段有值
  if (!receivableForm.feeAmount || receivableForm.feeAmount < 0.01) {
    receivableForm.feeAmount = 0.01;
  }

  // 延迟一下，确保表单状态已更新
  setTimeout(() => {
    receivableFormRef.value?.validate(async (errors) => {
      if (errors) {
        console.error("表单验证错误:", errors);
        return;
      }

      receivableSaving.value = true;
      try {
        // 将金额单位从元转换为分
        const amountInCents = Math.round(receivableForm.feeAmount * 100);

        // 构造保存的数据
        await arbApi.createArb({
          orderSn: receivableForm.orderSn,
          feeId: receivableForm.feeName, // 使用feeId字段传递应收科目
          feeTarget: receivableForm.feeTarget,
          feeAmount: amountInCents, // 传递分为单位的金额
        });

        messages.success("应收账款添加成功");
        receivableDialogVisible.value = false;

        // 刷新数据
        refreshData();
      } catch (error) {
        console.error("保存应收账款失败:", error);
        messages.error("保存应收账款失败");
      } finally {
        receivableSaving.value = false;
      }
    });
  }, 0);
}

function confirmPayment(row) {
  // 格式化金额
  const amount = row.feeAmount || 0;
  const amountYuan = (amount / 100).toFixed(2);
  const formattedAmount = `¥${Number(amountYuan).toLocaleString()}`;

  dialog.warning({
    title: "应收确认",
    content: `请确认金额：${formattedAmount}，确认后将不能修改。`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        // 使用通用的doPut方法更新对账状态
        const url = `/financial/receivable/bill`;
        await doPut(url, {
          id: row.id,
          confirmed: true,
        });

        messages.success(`应收金额 ${row.feeName || row.feeId} 已确认`);
        // 重新加载数据
        refreshData();
      } catch (error) {
        console.error("确认对账失败:", error);
        messages.error("确认对账失败");
      }
    },
  });
}

// 删除应收账款
function deleteReceivable(row) {
  dialog.warning({
    title: "删除确认",
    content: `确定要删除应收科目"${row.feeName}"吗？此操作不可恢复。`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        // 使用通用的doDelete方法删除应收账款
        const url = `/financial/receivable/bill/${row.id}`;
        await doDelete(url);

        messages.success("应收账款删除成功");
        // 刷新数据
        refreshData();
      } catch (error) {
        console.error("删除应收账款失败:", error);
        messages.error("删除应收账款失败");
      }
    },
  });
}

// 添加新的应收项目
function addNewItem(row) {
  // 显示新增应收账款弹窗
  showAddReceivableDialog(row);
}

// 确认整个订单的所有应收项
function confirmOrder(row) {
  // 获取订单ID和订单号
  const orderId = row.orderId;
  const orderSn = row.orderSn;

  // 检查是否有子项
  if (!row.children || row.children.length === 0) {
    messages.warning("该订单没有应收项，无法确认");
    return;
  }

  // 检查是否有订单ID
  if (!orderId) {
    messages.warning("无法获取订单ID，请联系管理员");
    return;
  }

  // 计算总金额
  const totalAmount = row.feeAmount || 0;
  const formattedAmount = `¥${Number(
    (totalAmount / 100).toFixed(2)
  ).toLocaleString()}`;

  dialog.warning({
    title: "订单确认",
    content: `确定要确认订单"${orderSn}"的所有应收项吗？总金额：${formattedAmount}，确认后将不能修改。`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const url = `/financial/receivable/bill/confirmed`;
        await doPut(url, {
          orderId: orderId, // 使用真正的订单ID
          confirmed: true,
        });

        messages.success(`订单 ${orderSn} 的所有应收项已确认`);
        // 重新加载数据
        refreshData();
      } catch (error) {
        console.error("确认订单失败:", error);
        messages.error("确认订单失败");
      }
    },
  });
}

// 获取行的唯一标识
function getRowKey(row) {
  // 确保每行都有一个唯一的key
  if (row.id) {
    return row.id;
  }

  // 如果没有id，但有feeId，使用feeId作为key
  if (row.feeId) {
    return `fee-${row.feeId}`;
  }

  // 如果没有id和feeId，但有orderSn，使用orderSn作为key
  if (row.orderSn) {
    return `order-${row.orderSn}`;
  }

  // 如果以上都没有，生成一个随机key
  return `row-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}
</script>

<style scoped>
.accounts-receivable-page {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.toolbar {
  margin-bottom: 0;
}

.filter-card {
  margin-bottom: 0;
}

/* 筛选区域样式 */
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: flex-start;
}

.filter-label {
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
  line-height: 32px;
  color: #333;
}

.filter-options {
  flex: 1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.n-radio-group) {
  flex-wrap: wrap;
}

:deep(.n-radio-button) {
  margin-bottom: 8px;
}

.custom-date-picker {
  width: 280px;
}

/* 自定义单选按钮组样式 */
:deep(.custom-radio-group .custom-radio-button) {
  border: none;
  background: transparent;
  transition: all 0.2s;
}

:deep(.custom-radio-group .custom-radio-button:hover) {
  color: var(--primary-color);
  background-color: rgba(24, 160, 88, 0.1);
}

:deep(.custom-radio-group .custom-radio-button--checked) {
  color: #fff !important;
  background-color: var(--primary-color) !important;
  border: none !important;
}

:deep(.n-data-table) {
  margin-top: 0;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  flex: 1;
}

:deep(.n-data-table-th) {
  background-color: #f9f9f9 !important;
  font-weight: 600;
  padding: 12px 8px;
}

:deep(.n-data-table-td) {
  padding: 10px 8px;
}

:deep(.n-button) {
  margin-right: 4px;
}

/* 表格行样式 */
:deep(.n-data-table-tr) {
  transition: background-color 0.3s;
}

:deep(.n-data-table-tr:hover) {
  background-color: #f5f7fa;
}

/* 子行样式 */
:deep(.n-data-table-tr[data-row-key^="fee-"]) {
  background-color: #f9fbff;
}

:deep(.n-data-table-tr[data-row-key^="fee-"]:hover) {
  background-color: #f0f7ff;
}

/* 确保表头和内容对齐 */
:deep(.n-data-table-th .n-data-table-th__title) {
  display: flex;
  justify-content: center;
}

:deep(.n-data-table-th[data-col-key="orderSn"] .n-data-table-th__title) {
  justify-content: flex-start;
}

:deep(.n-data-table-th[data-col-key="feeAmount"] .n-data-table-th__title) {
  justify-content: center;
}

/* 确保所有单元格内容居中 */
:deep(.n-data-table-td .n-data-table-td__content) {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 订单号列左对齐 */
:deep(.n-data-table-td[data-col-key="orderSn"] .n-data-table-td__content) {
  justify-content: flex-start;
}

/* 金额列居中对齐 */
:deep(.n-data-table-td[data-col-key="feeAmount"] .n-data-table-td__content) {
  justify-content: center;
}

/* 添加以下样式来增加图标之间的间距 */
:deep(.n-icon) {
  margin: 0 2px;
}

/* 操作列图标样式 */
:deep(.n-data-table-td[data-col-key="actions"] .n-icon) {
  font-size: 24px;
  margin: 0 4px;
  transition: transform 0.3s;
}

:deep(.n-data-table-td[data-col-key="actions"] .n-button:hover .n-icon) {
  transform: scale(1.2);
}

/* 为操作栏添加新的样式 */
:deep(.n-data-table-td[data-col-key="actions"] .n-space) {
  gap: 4px !important;
}

/* 优化按钮样式 */
:deep(.n-button.n-button--quaternary) {
  padding: 4px 12px;
  height: 32px;
  line-height: 24px;
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.n-data-table-td[data-col-key="actions"] .n-button) {
  min-width: 40px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.n-button.n-button--quaternary:hover) {
  background-color: rgba(24, 160, 88, 0.15);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

:deep(.n-button.n-button--quaternary.n-button--primary:hover) {
  background-color: rgba(32, 128, 240, 0.15);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}
</style>
