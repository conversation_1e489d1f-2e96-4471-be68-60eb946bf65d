// 角色编辑弹窗样式

.role-edit-modal {

  // 全屏弹窗样式
  :deep(.n-card) {
    width: 100vw !important;
    height: 100vh !important;
    margin: 0 !important;
    border-radius: 0 !important;
    max-width: none !important;
    max-height: none !important;
    display: flex !important;
    flex-direction: column !important;
  }

  :deep(.n-card__content) {
    flex: 1 !important;
    padding: 0 !important;
    overflow: hidden !important;
    min-height: 0 !important; // 关键：允许flex子元素收缩
  }

  :deep(.n-card__footer) {
    flex-shrink: 0 !important; // 防止footer被压缩
    padding: 20px 24px !important;
    border-top: 1px solid #e0e0e6 !important;
    background-color: #ffffff !important;
    z-index: 10 !important;
  }
}

// 主容器布局
.role-edit-container {
  display: flex;
  height: 100%;
  min-height: 0; // 允许flex子元素收缩
  background-color: #f8f9fa;
}

// 左侧表单区域
.form-section {
  width: 400px;
  background-color: #ffffff;
  border-right: 1px solid #e0e0e6;
  display: flex;
  flex-direction: column;

  .form-header {
    padding: 24px;
    border-bottom: 1px solid #e0e0e6;
    background-color: #fafafa;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .form-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;

    :deep(.n-form-item) {
      margin-bottom: 24px;
    }

    :deep(.n-form-item-label) {
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;
    }

    :deep(.n-input) {
      border-radius: 6px;
    }
  }
}

// 右侧权限树区域
.permissions-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 0; // 允许flex子元素收缩
  // 计算最大高度：100vh - 弹窗header(60px) - 弹窗footer(80px) - 上下margin(32px)
  max-height: calc(100vh - 172px);
  overflow: hidden; // 防止内容溢出

  .permissions-header {
    flex-shrink: 0; // 防止header被压缩
    padding: 20px 24px;
    border-bottom: 1px solid #e0e0e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fafafa;
    border-radius: 8px 8px 0 0;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .permissions-actions {
      display: flex;
      gap: 8px;

      :deep(.n-button) {
        border-radius: 4px;
      }
    }
  }

  .permissions-content {
    flex: 1;
    padding: 20px 24px;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0; // 允许flex子元素收缩
    // 计算最大高度：权限区域最大高度 - header高度(约80px)
    max-height: calc(100vh - 252px);
    // 确保滚动行为平滑
    scroll-behavior: smooth;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    // 权限树样式优化
    :deep(.n-tree) {
      // 确保树容器不会溢出
      width: 100%;
      height: 100%;

      .n-tree-node {
        margin-bottom: 4px;

        .n-tree-node-content {
          // padding: 8px 12px;
          border-radius: 6px;
          transition: all 0.2s ease;
          word-wrap: break-word; // 防止长文本溢出
          white-space: normal; // 允许文本换行
          display: flex !important;
          align-items: center !important;
          // min-height: 32px; // 确保有足够的高度

          &:hover {
            background-color: #f5f7fa;
          }
        }

        .n-tree-node-content--selected {
          background-color: #e6f7ff;
          border: 1px solid #91d5ff;
        }

        .n-checkbox {
          margin-right: 8px;
          flex-shrink: 0; // 防止复选框被压缩
          display: flex !important;
          align-items: center !important;

          .n-checkbox__input {
            display: flex !important;
            align-items: center !important;
          }

          .n-checkbox-box {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
          }
        }

        .n-tree-node-content__text {
          font-size: 14px;
          color: #333;
          font-weight: 500;
          line-height: 1.5;
          display: flex;
          align-items: center;
          flex: 1; // 占据剩余空间
        }
      }

      // 根节点样式
      .n-tree-node--root {
        >.n-tree-node-content {
          .n-tree-node-content__text {
            font-weight: 600;
            color: #1890ff;
          }
        }
      }

      // 叶子节点样式
      .n-tree-node--leaf {
        >.n-tree-node-content {
          .n-tree-node-content__text {
            color: #666;
          }
        }
      }

      // 展开/收起图标样式
      .n-tree-node-switcher {
        width: 20px;
        height: 20px;
        margin-right: 4px;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        flex-shrink: 0; // 防止图标被压缩
      }

      // 树节点整体布局优化
      .n-tree-node-wrapper {
        display: flex !important;
        align-items: center !important;
      }

      // 树节点缩进区域
      .n-tree-node-indent {
        display: flex !important;
        align-items: center !important;
      }
    }
  }
}

// 底部按钮区域
.modal-footer {
  padding: 0;

  :deep(.n-button) {
    min-width: 100px;
    border-radius: 6px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .form-section {
    width: 350px;
  }
}

@media (max-width: 768px) {
  .role-edit-container {
    flex-direction: column;
  }

  .form-section {
    width: 100%;
    height: 300px;
    flex-shrink: 0; // 防止表单区域被压缩
  }

  .permissions-section {
    margin: 8px;
    border-radius: 0;
    flex: 1;
    min-height: 0; // 确保权限树区域可以收缩
    // 小屏幕下调整最大高度
    max-height: calc(100vh - 472px); // 表单高度300px + 其他元素高度

    .permissions-content {
      // 小屏幕下调整内容区域最大高度
      max-height: calc(100vh - 552px);
    }
  }
}