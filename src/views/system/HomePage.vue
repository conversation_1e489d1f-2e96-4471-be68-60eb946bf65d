<template>
  <div class="home-page">
    <!-- 我的应用区域 -->
    <n-card title="我的应用" class="app-card">
      <n-spin :show="loading">
        <template #description>
          <span>加载中...</span>
        </template>
        <div class="app-list" v-if="!loading">
          <div v-for="app in appList" :key="app.id" class="app-item" @click="navigateToApp(app.path)">
            <n-icon :color="app.color" size="36">
              <component :is="getIconComponent(app.icon)" />
            </n-icon>
            <div class="app-name">{{ app.name }}</div>
          </div>
        </div>
        <div v-else class="loading-placeholder"></div>
      </n-spin>
    </n-card>

    <!-- 我的流程数据区域 -->
    <n-card title="流程中心" class="workflow-card">
      <div class="workflow-list">
        <!-- 横向排列的流程数据项 -->
        <!-- 我的待办 -->
        <div class="workflow-item" @click="navigateTo('/workflow/tasks')">
          <div class="workflow-icon">
            <n-icon size="36" color="#2080f0">
              <CheckmarkDoneCircleOutline />
            </n-icon>
          </div>
          <div class="workflow-info">
            <div class="workflow-name">我的待办</div>
            <div class="workflow-count" v-if="workflowStats.myTasks > 0">{{ workflowStats.myTasks }}</div>
          </div>
        </div>

        <!-- 我发起的 -->
        <div class="workflow-item" @click="navigateTo('/workflow/initiated')">
          <div class="workflow-icon">
            <n-icon size="36" color="#18a058">
              <SendOutline />
            </n-icon>
          </div>
          <div class="workflow-info">
            <div class="workflow-name">我发起的</div>
            <div class="workflow-count" v-if="workflowStats.myInitiated > 0">{{ workflowStats.myInitiated }}</div>
          </div>
        </div>

        <!-- 我处理的 -->
        <div class="workflow-item" @click="navigateTo('/workflow/processed')">
          <div class="workflow-icon">
            <n-icon size="36" color="#d03050">
              <DocumentTextOutline />
            </n-icon>
          </div>
          <div class="workflow-info">
            <div class="workflow-name">我处理的</div>
            <div class="workflow-count" v-if="workflowStats.myProcessed > 0">{{ workflowStats.myProcessed }}</div>
          </div>
        </div>

        <!-- 抄送我的 -->
        <div class="workflow-item" @click="navigateTo('/workflow/copied')">
          <div class="workflow-icon">
            <n-icon size="36" color="#f0a020">
              <CopyOutline />
            </n-icon>
          </div>
          <div class="workflow-info">
            <div class="workflow-name">抄送我的</div>
            <div class="workflow-count" v-if="workflowStats.myCopied > 0">{{ workflowStats.myCopied }}</div>
          </div>
        </div>

        <!-- 发起流程 -->
        <div class="workflow-item" @click="navigateTo('/workflow/initiate')">
          <div class="workflow-icon">
            <n-icon size="36" color="#8a2be2">
              <AddCircleOutline />
            </n-icon>
          </div>
          <div class="workflow-info">
            <div class="workflow-name">发起流程</div>
          </div>
        </div>

        <!-- 待办委托 -->
        <div class="workflow-item" @click="navigateTo('/workflow/delegate')">
          <div class="workflow-icon">
            <n-icon size="36" color="#0096c7">
              <PeopleOutline />
            </n-icon>
          </div>
          <div class="workflow-info">
            <div class="workflow-name">待办委托</div>
          </div>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { NCard, NIcon, NSpin } from 'naive-ui'
import {
  CheckmarkDoneCircleOutline,
  SendOutline,CarSportOutline,
  DocumentTextOutline,
  CopyOutline,
  AddCircleOutline,
  PeopleOutline,
  BagHandleOutline,
  ArchiveOutline,
  PeopleOutline as PeopleIcon,
  CashOutline,
  PersonOutline
} from '@vicons/ionicons5'
import { workflowApi } from '@/api/workflow'
import { appList as mockAppList } from '@/mock/workflowData'

const router = useRouter()
const workflowStats = ref({
  myTasks: 0,
  myInitiated: 0,
  myProcessed: 0,
  myCopied: 0
})
const menus = ref([])
const filteredAppList = ref([])

// 过滤应用列表，只显示在menus中的应用
const filterAppList = () => {
  if (!menus.value || menus.value.length === 0) {
    filteredAppList.value = mockAppList
    return
  }

  filteredAppList.value = mockAppList.filter(app => {
    // 从应用路径中提取应用ID
    const appPath = app.path.split('?')[0]
    const appId = appPath.replace('/app/index/', '')

    // 检查应用ID是否在menus中
    return menus.value.some(menu => menu.id === Number(appId))
  })
}

const appList = computed(() => {
  return filteredAppList.value
})

// 获取图标组件
const getIconComponent = (iconName) => {
  const iconMap = {
    'BagHandle': BagHandleOutline,
    'Archive': ArchiveOutline,
    'People': PeopleIcon,
    'Cash': CashOutline,
    'Person': PersonOutline,
    'CarSportOutline':CarSportOutline,
  }
  return iconMap[iconName] || PersonOutline
}

// 导航到应用
const navigateToApp = (path) => {
  router.push(path)
}

// 导航到指定路径
const navigateTo = (path) => {
  router.push(path)
}

// 获取工作流程统计数据
const fetchWorkflowStats = async () => {
  try {
    const response = await workflowApi.getWorkflowStats()
    if (response.code === 200) {
      workflowStats.value = response.data
    }
  } catch (error) {
    console.error('获取工作流程统计数据失败:', error)
  }
}

// 加载状态
const loading = ref(false)
const retryCount = ref(0)
const maxRetries = 5
const retryInterval = 1000 // 1秒

// 从localStorage获取menus数据
const getMenusFromLocalStorage = () => {
  loading.value = true

  const tryGetMenus = () => {
    try {
      const menusStr = localStorage.getItem('menus')
      if (menusStr) {
        menus.value = JSON.parse(menusStr)
        // 过滤应用列表
        filterAppList()
        loading.value = false
        return true
      } else {
        console.log(`localStorage中没有menus数据，第${retryCount.value + 1}次尝试`)
        return false
      }
    } catch (error) {
      console.error('解析menus数据失败:', error)
      return false
    }
  }

  const retryGetMenus = () => {
    if (tryGetMenus()) {
      return // 获取成功，不再重试
    }

    retryCount.value++

    if (retryCount.value < maxRetries) {
      // 继续重试
      setTimeout(retryGetMenus, retryInterval)
    } else {
      // 超过最大重试次数
      console.log(`已尝试${maxRetries}次获取menus数据，但未成功`)
      // 使用所有应用
      filteredAppList.value = mockAppList
      loading.value = false
    }
  }

  // 开始尝试获取
  retryGetMenus()
}

onMounted(() => {
  fetchWorkflowStats()
  getMenusFromLocalStorage()
})
</script>

<style scoped>
.home-page {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 应用卡片样式 */
.app-card {
  margin-bottom: 20px;
}

.app-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  /* justify-content: center; */
}

.app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  border-radius: 8px;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.app-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.app-name {
  margin-top: 10px;
  font-size: 14px;
  text-align: center;
}

/* 工作流程卡片样式 */
.workflow-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 20px;
  padding: 10px 0;
  justify-content: center;
}

.workflow-item {
  display: flex;
  align-items: center;
  padding: 20px;
  width: calc(25% - 14px);
  border-radius: 12px;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

@media (max-width: 1200px) {
  .workflow-item {
    width: calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .workflow-item {
    width: 100%;
  }
}

.workflow-item:hover {
  background-color: #e8f4ff;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.workflow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: rgba(255, 255, 255, 0.9);
  margin-right: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.workflow-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.workflow-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.workflow-count {
  display: inline-block;
  font-size: 18px;
  font-weight: bold;
  color: #2080f0;
  margin-top: 5px;
}

.loading-placeholder {
  height: 120px;
  width: 100%;
}


</style>