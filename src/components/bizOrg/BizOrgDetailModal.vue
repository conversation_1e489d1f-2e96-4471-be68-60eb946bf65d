<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    title="机构详情"
    style="width: 800px; max-width: 90vw"
    :mask-closable="true"
    :closable="true"
  >
    <n-spin :show="loading">
      <div v-if="orgData" class="detail-content">
        <n-grid :cols="2" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <div class="detail-item">
              <div class="detail-label">机构编码</div>
              <div class="detail-value">{{ orgData.orgCode }}</div>
            </div>
          </n-grid-item>

          <n-grid-item>
            <div class="detail-item">
              <div class="detail-label">机构名称</div>
              <div class="detail-value">{{ orgData.orgName }}</div>
            </div>
          </n-grid-item>

          <n-grid-item>
            <div class="detail-item">
              <div class="detail-label">省/直辖市</div>
              <div class="detail-value">
                {{ getProvinceLabel(orgData.province) }}
              </div>
            </div>
          </n-grid-item>

          <n-grid-item>
            <div class="detail-item">
              <div class="detail-label">市/区</div>
              <div class="detail-value">{{ getCityLabel(orgData.city) }}</div>
            </div>
          </n-grid-item>

          <n-grid-item>
            <div class="detail-item">
              <div class="detail-label">主营品牌</div>
              <div class="detail-value">
                <n-tag type="info">{{ orgData.mainBrand }}</n-tag>
              </div>
            </div>
          </n-grid-item>

          <n-grid-item>
            <div class="detail-item">
              <div class="detail-label">机构类型</div>
              <div class="detail-value">
                <n-tag :type="getOrgTypeTagType(orgData.orgType)">
                  {{ getOrgTypeLabel(orgData.orgType) }}
                </n-tag>
              </div>
            </div>
          </n-grid-item>

          <n-grid-item>
            <div class="detail-item">
              <div class="detail-label">联系人</div>
              <div class="detail-value">{{ orgData.contactPerson }}</div>
            </div>
          </n-grid-item>

          <n-grid-item>
            <div class="detail-item">
              <div class="detail-label">联系电话</div>
              <div class="detail-value">{{ orgData.contactPhone }}</div>
            </div>
          </n-grid-item>

          <n-grid-item>
            <div class="detail-item">
              <div class="detail-label">成立日期</div>
              <div class="detail-value">
                {{
                  orgData.establishDate
                    ? formatDate(orgData.establishDate)
                    : "未设置"
                }}
              </div>
            </div>
          </n-grid-item>

          <n-grid-item>
            <div class="detail-item">
              <div class="detail-label">状态</div>
              <div class="detail-value">
                <n-tag
                  :type="orgData.status === 'active' ? 'success' : 'error'"
                >
                  {{ orgData.status === "active" ? "正常" : "停用" }}
                </n-tag>
              </div>
            </div>
          </n-grid-item>

          <n-grid-item :span="2">
            <div class="detail-item">
              <div class="detail-label">业务权限</div>
              <div class="detail-value">
                <n-space
                  v-if="
                    orgData.businessPermissions &&
                    orgData.businessPermissions.length > 0
                  "
                >
                  <n-tag
                    v-for="permission in orgData.businessPermissions"
                    :key="permission"
                    size="small"
                    type="default"
                  >
                    {{ getPermissionLabel(permission) }}
                  </n-tag>
                </n-space>
                <span v-else class="no-data">无权限</span>
              </div>
            </div>
          </n-grid-item>

          <n-grid-item :span="2">
            <div class="detail-item">
              <div class="detail-label">详细地址</div>
              <div class="detail-value">{{ orgData.address || "未填写" }}</div>
            </div>
          </n-grid-item>

          <n-grid-item :span="2">
            <div class="detail-item">
              <div class="detail-label">备注</div>
              <div class="detail-value">{{ orgData.remark || "无" }}</div>
            </div>
          </n-grid-item>

          <n-grid-item>
            <div class="detail-item">
              <div class="detail-label">创建时间</div>
              <div class="detail-value">
                {{ formatDateTime(orgData.createdAt) }}
              </div>
            </div>
          </n-grid-item>

          <n-grid-item>
            <div class="detail-item">
              <div class="detail-label">更新时间</div>
              <div class="detail-value">
                {{ formatDateTime(orgData.updatedAt) }}
              </div>
            </div>
          </n-grid-item>
        </n-grid>
      </div>

      <div v-else-if="!loading" class="no-data">
        <n-empty description="暂无数据" />
      </div>
    </n-spin>

    <template #footer>
      <n-space justify="end">
        <n-button @click="visible = false">关闭</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import bizOrgApi from "@/api/bizOrg";
import messages from "@/utils/messages";

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  id: {
    type: [Number, String],
    default: null,
  },
});

// Emits
const emit = defineEmits(["update:visible"]);

// 状态变量
const loading = ref(false);
const orgData = ref(null);

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

// 监听ID变化，加载数据
watch(
  () => props.id,
  (newId) => {
    if (newId && props.visible) {
      loadOrgData(newId);
    }
  },
  { immediate: true }
);

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.id) {
      loadOrgData(props.id);
    }
  }
);

// 加载机构数据
const loadOrgData = async (id) => {
  if (!id) return;

  loading.value = true;
  try {
    const response = await bizOrgApi.getBizOrgDetail(id);
    if (response.code === 200) {
      orgData.value = response.data;
    } else {
      messages.error(response.message || "获取机构详情失败");
    }
  } catch (error) {
    console.error("获取机构详情失败:", error);
    messages.error("获取机构详情失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "";
  const date = new Date(dateStr);
  return date.toLocaleDateString("zh-CN");
};

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return "";
  const date = new Date(dateStr);
  return date.toLocaleString("zh-CN");
};

// 获取省份标签
const getProvinceLabel = (value) => {
  const provinceMap = {
    shandong: "山东",
    hebei: "河北",
    henan: "河南",
    chongqing: "重庆",
  };
  return provinceMap[value] || value;
};

// 获取城市标签
const getCityLabel = (value) => {
  const cityMap = {
    // 山东省城市
    jinan: "济南市",
    dezhou: "德州市",
    zibo: "淄博市",
    linyi: "临沂市",
    liaocheng: "聊城市",
    dongying: "东营市",
    binzhou: "滨州市",
    // 河南省城市
    zhengzhou: "郑州市",
    luoyang: "洛阳市",
    zhumadian: "驻马店市",
    // 河北省城市
    shijiazhuang: "石家庄市",
    cangzhou: "沧州市",
    langfang: "廊坊市",
    // 重庆市区县
    yuzhong: "渝中区",
    jiangbei: "江北区",
  };
  return cityMap[value] || value;
};

// 获取机构类型标签
const getOrgTypeLabel = (value) => {
  const typeMap = {
    group: "集团",
    single_store: "单店",
    secondary_network: "二网",
  };
  return typeMap[value] || value;
};

// 获取机构类型标签类型
const getOrgTypeTagType = (value) => {
  const typeMap = {
    group: "success",
    single_store: "info",
    secondary_network: "warning",
  };
  return typeMap[value] || "default";
};

// 获取权限标签
const getPermissionLabel = (value) => {
  const permissionMap = {
    can_stock_in: "可入库",
    can_sell: "可销售",
    can_stock_out: "可出库",
    can_settle: "可结算",
  };
  return permissionMap[value] || value;
};
</script>

<style scoped>
.detail-content {
  padding: 16px 0;
}

.detail-item {
  margin-bottom: 16px;
}

.detail-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #333;
  word-break: break-all;
}

.no-data {
  color: #999;
  font-style: italic;
}

:deep(.n-tag) {
  margin-right: 8px;
  margin-bottom: 4px;
}
</style>
