<template>
  <div class="recept-form-container">
    <!-- 收款单基本信息部分 -->
    <div class="form-section">
      <div class="section-header">
        <h3>基础信息</h3>
      </div>
      <n-form
        ref="receptFormRef"
        :model="receptForm"
        :rules="receptRules"
        label-placement="top"
        require-mark-placement="right-hanging"
      >
        <n-grid :cols="4" :x-gap="16">
          <n-grid-item>
            <n-form-item label="收款单位" path="receptOrgId">
              <department-selector
                v-model="selectedDepartment"
                mode="single"
                label="请选择收款单位"
                @update:model-value="handleDepartmentChange"
                width="100%"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="收款方式" path="receptMethod">
              <n-select
                v-model:value="receptForm.receptMethod"
                :options="accountOptions"
                placeholder="请选择收款方式"
                @update:value="handleAccountChange"
                :status="receptForm.receptMethod ? 'success' : undefined"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="收款时间" path="receptTime">
              <n-date-picker
                v-model:value="receptForm.receptTime"
                type="datetime"
                clearable
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="收款金额（元）" path="receptAmount">
              <n-input-number
                v-model:value="receptForm.receptAmount"
                :min="0.01"
                :precision="2"
                button-placement="both"
                :step="1000"
                style="width: 100%"
                :default-value="0.01"
                :validator="(value) => value >= 0.01"
                disabled
              >
                <template #prefix> ¥ </template>
              </n-input-number>
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="收款金额（大写）">
              <n-input v-model:value="amountInChinese" disabled />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="业务流水号">
              <n-input
                v-model:value="receptForm.bizNo"
                placeholder="请输入业务流水号"
                maxlength="20"
                clearable
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="收款摘要" path="receptSummary">
              <n-input
                v-model:value="receptForm.receptSummary"
                placeholder="请输入收款摘要"
                maxlength="100"
                clearable
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </n-form>
    </div>

    <!-- 收款明细数据列表部分 -->
    <div class="form-section">
      <div class="section-header">
        <h3>收款明细</h3>
        <div class="button-group">
          <n-button type="primary" size="small" @click="showReceivableSelector">
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            应收账款
          </n-button>
          <n-button type="info" size="small" @click="addOtherRecept">
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            其他收款
          </n-button>
        </div>
      </div>

      <!-- 收款明细数据表格 -->
      <n-data-table
        :columns="receptDetailColumns"
        :data="receivableItems"
        :bordered="false"
        :single-line="false"
        :pagination="false"
        size="small"
      >
        <template #empty>
          <n-empty description="请选择应收账款或手动添加收款明细" />
        </template>
      </n-data-table>

      <!-- 合计行 -->
      <div v-if="receivableItems.length > 0" class="total-row">
        <span>合计：</span>
        <span class="total-amount"
          >¥{{ formatMoney(totalReceivableAmount) }}</span
        >
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="form-footer">
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSubmit" :loading="submitting"
          >保存</n-button
        >
      </n-space>
    </div>
  </div>

  <!-- 应收账款选择器 -->
  <receivable-selector
    v-model:visible="receivableSelectorVisible"
    :multiple="multiple"
    :initial-selected="selectedReceivables"
    @select="handleReceivableSelected"
    @selectMultiple="handleReceivablesMultipleSelected"
    @cancel="handleReceivableSelectCancel"
  />
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, h } from "vue";
import {
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NInputNumber,
  NButton,
  NGrid,
  NGridItem,
  NDataTable,
  NSpace,
  NIcon,
  NEmpty,
  NDatePicker,
} from "naive-ui";
import {
  AddOutline,
  TrashOutline,
  CheckmarkOutline,
  PencilOutline,
} from "@vicons/ionicons5";
import { getDictOptions } from "@/api/dict";
import { convertNumberToChinese } from "@/utils/money";
import messages from "@/utils/messages";
import ReceivableSelector from "@/components/financial/ReceivableSelector.vue";
import DepartmentSelector from "@/components/users/DepartmentSelector.vue";
import accountsApi from "@/api/accounts";

// 定义组件属性
const props = defineProps({
  // 初始表单数据
  initialData: {
    type: Object,
    default: () => ({
      id: null,
      receptOrgId: 0,
      receptMethod: null, // 收款方式
      receptAmount: 0.01, // 默认值设为0.01，单位是元
      receptAmountCn: "", // 收款金额大写
      receptTime: null, // 收款时间
      bizNo: "", // 业务流水号，选填
      receptSummary: "", // 收款摘要
      items: [], // 收款明细项
    }),
  },
  // 是否编辑模式
  isEdit: {
    type: Boolean,
    default: false,
  },
  // 是否允许多选应收账款
  multiple: {
    type: Boolean,
    default: true,
  },
  // 初始选中的应收账款（单选模式）
  initialReceivable: {
    type: Object,
    default: null,
  },
  // 初始选中的应收账款列表（多选模式）
  initialReceivables: {
    type: Array,
    default: () => [],
  },
});

// 定义组件事件
const emit = defineEmits([
  "submit", // 提交表单
  "cancel", // 取消操作
  "update:receivable", // 更新选中的应收账款（单选模式）
  "update:receivables", // 更新选中的应收账款列表（多选模式）
]);

// 表单引用
const receptFormRef = ref(null);
const submitting = ref(false);

// 表单数据
const receptForm = reactive({
  id: props.initialData.id,
  receptOrgId: props.initialData.receptOrgId || 0,
  receptMethod: props.initialData.receptMethod || null, // 收款方式（原receptAccount）
  receptAmount: props.initialData.receptAmount || 0.01, // 默认值设为0.01，单位是元
  receptAmountCn: props.initialData.receptAmountCn || "", // 收款金额大写
  receptTime: props.initialData.receptTime || Date.now(), // 默认为当前时间戳
  bizNo: props.initialData.bizNo || "", // 业务流水号，选填
  receptSummary: props.initialData.receptSummary || "", // 收款摘要
  items: [], // 收款明细项
});

// 应收账款选择器相关
const receivableSelectorVisible = ref(false);
const selectedReceivable = ref(props.initialReceivable);
const selectedReceivables = ref(props.initialReceivables || []);

// 部门选择器相关
const selectedDepartment = ref(null);

// 应收账款数据项
const receivableItems = ref([]);

// 收款账户选项
const accountOptions = ref([]);
// 收款科目选项
const subjectOptions = ref([]);

// 收款金额大写
const amountInChinese = computed(() => {
  if (!receptForm.receptAmount || receptForm.receptAmount <= 0) {
    return "零元整";
  }
  // 输入框中是元为单位，直接传递给转换函数
  return convertNumberToChinese(receptForm.receptAmount);
});

// 应收账款总金额
const totalReceivableAmount = computed(() => {
  return (
    receivableItems.value.reduce(
      (sum, item) => sum + (item.feeAmount || 0),
      0
    ) / 100
  ); // 分转元
});

// 格式化金额显示
function formatMoney(amount) {
  if (amount === undefined || amount === null) return "0.00";
  return amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

// 收款明细表格列定义
const receptDetailColumns = [
  {
    title: "收款科目",
    key: "feeName",
    width: 200,
    render(row) {
      // 如果是其他收款且处于编辑状态，显示下拉选择框
      if (row.isOtherRecept && row.isEditing) {
        return h(NSelect, {
          value: row.feeName,
          options: subjectOptions.value,
          placeholder: "请选择收款科目",
          clearable: false,
          style: { width: "100%" },
          onUpdateValue: (value) => {
            row.feeName = value;
            // 保存选中的科目标签，用于显示
            const selectedOption = subjectOptions.value.find(
              (opt) => opt.value === value
            );
            if (selectedOption) {
              row.feeNameLabel = selectedOption.label;
            }
          },
        });
      }
      // 应收账款行不允许修改科目，即使处于编辑状态
      // 否则显示文本
      // 如果有feeNameLabel（保存的中文名称），优先显示它
      return row.feeNameLabel || row.feeName;
    },
  },
  {
    title: "收款金额",
    key: "feeAmount",
    width: 150,
    render(row) {
      // 如果处于编辑状态，显示数字输入框（无论是应收账款还是其他收款）
      if (row.isEditing) {
        return h(NInputNumber, {
          value: row.feeAmount / 100, // 分转元
          min: 0.01,
          precision: 2,
          step: 1000,
          buttonPlacement: "both",
          style: { width: "100%" },
          onUpdateValue: (value) => {
            if (value) {
              row.feeAmount = Math.round(value * 100); // 元转分
              // 更新总金额
              updateTotalAmount();
            }
          },
        });
      }
      // 否则显示格式化的金额
      const amount = row.feeAmount || 0;
      return `￥${(amount / 100).toLocaleString("zh-CN", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })}`;
    },
  },
  {
    title: "收款摘要",
    key: "summary",
    width: 200,
    render(row) {
      // 如果处于编辑状态，显示文本输入框（无论是应收账款还是其他收款）
      if (row.isEditing) {
        return h(NInput, {
          value:
            row.summary ||
            (row.orderSn ? `${row.customerName}(${row.mobile})` : ""),
          placeholder: "请输入收款摘要",
          onUpdateValue: (value) => {
            row.summary = value;
          },
        });
      }

      // 如果是应收账款且有自定义摘要，优先显示自定义摘要
      if (row.orderSn) {
        return row.summary || `${row.customerName}(${row.mobile})`;
      }
      // 如果是其他收款，显示摘要
      return row.summary || "其他收款";
    },
  },
  {
    title: "操作",
    key: "actions",
    width: 120,
    align: "center",
    render(row) {
      const buttons = [];

      // 如果处于编辑状态，显示确认按钮（无论是应收账款还是其他收款）
      if (row.isEditing) {
        buttons.push(
          h(
            NButton,
            {
              size: "small",
              quaternary: true,
              type: "success",
              onClick: () => confirmEditRow(row),
            },
            {
              default: () =>
                h(NIcon, null, { default: () => h(CheckmarkOutline) }),
            }
          )
        );
      }

      // 如果不处于编辑状态，显示编辑按钮（无论是应收账款还是其他收款）
      if (!row.isEditing) {
        buttons.push(
          h(
            NButton,
            {
              size: "small",
              quaternary: true,
              type: "info",
              onClick: () => editRow(row),
            },
            {
              default: () =>
                h(NIcon, null, { default: () => h(PencilOutline) }),
            }
          )
        );
      }

      // 所有行都显示删除按钮
      buttons.push(
        h(
          NButton,
          {
            size: "small",
            quaternary: true,
            type: "error",
            onClick: () => removeReceivableItem(row),
          },
          {
            default: () => h(NIcon, null, { default: () => h(TrashOutline) }),
          }
        )
      );

      return h(NSpace, { size: "small" }, { default: () => buttons });
    },
  },
];

// 表单验证规则
const receptRules = {
  receptOrgId: {
    required: true,
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择收款单位");
      }
      return true;
    },
    trigger: ["blur", "change", "input", "update"],
  },
  receptMethod: {
    required: true,
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择收款方式");
      }
      return true;
    },
    trigger: ["blur", "change", "input", "update"],
  },
  receptAmount: {
    required: true,
    validator: (_, value) => {
      if (value === null || value === undefined || value === "") {
        return new Error("请输入收款金额");
      }
      if (typeof value === "number" && value < 0.01) {
        return new Error("收款金额必须大于0");
      }
      return true;
    },
    trigger: ["blur", "change", "input"],
  },
  receptTime: {
    required: true,
    message: "请选择收款时间",
    trigger: ["blur", "change"],
  },
  receptSummary: {
    required: false,
    trigger: ["blur", "change"],
  },
};

// 生命周期钩子
onMounted(async () => {
  await fetchDictOptions();

  // 如果是编辑模式，初始化表单数据
  if (props.isEdit && props.initialData) {
    Object.assign(receptForm, props.initialData);

    // 如果有收款单位ID，初始化selectedDepartment
    if (receptForm.receptOrgId) {
      selectedDepartment.value = {
        id: receptForm.receptOrgId,
        name: "已选择的部门", // 这里可能需要通过API获取部门名称
      };

      // 获取该机构的可用收款账户
      await fetchOrgAccounts(receptForm.receptOrgId);
    }
  }

  // 初始化应收账款数据项
  initReceivableItems();
});

// 初始化应收账款数据项
function initReceivableItems() {
  // 不再清空现有列表
  // receivableItems.value = [];

  // 如果有初始选中的应收账款，添加到列表
  if (
    props.multiple &&
    props.initialReceivables &&
    props.initialReceivables.length > 0
  ) {
    // 使用循环添加每一项，避免重复
    for (const receivable of props.initialReceivables) {
      addReceivableToList(receivable);
    }
  } else if (props.initialReceivable) {
    // 添加单个应收账款
    addReceivableToList(props.initialReceivable);
  }

  // 更新总金额
  updateTotalAmount();
}

// 更新总金额
function updateTotalAmount() {
  if (receivableItems.value.length > 0) {
    const totalAmount = receivableItems.value.reduce(
      (sum, item) => sum + (item.feeAmount || 0),
      0
    );
    receptForm.receptAmount = totalAmount / 100; // 将分转换为元
  } else {
    receptForm.receptAmount = 0.01;
  }
}

// 监听 props 变化
watch(
  () => props.initialData,
  (newVal) => {
    if (newVal) {
      Object.assign(receptForm, newVal);
    }
  },
  { deep: true }
);

watch(
  () => props.initialReceivable,
  (newVal) => {
    selectedReceivable.value = newVal;
    if (newVal) {
      // 添加到应收账款列表
      addReceivableToList(newVal);
    }
  },
  { deep: true }
);

watch(
  () => props.initialReceivables,
  (newVal) => {
    selectedReceivables.value = newVal || [];
    if (newVal && newVal.length > 0) {
      // 将新的应收账款添加到列表中，而不是初始化整个列表
      for (const receivable of newVal) {
        addReceivableToList(receivable);
      }

      // 更新总金额
      updateTotalAmount();
    }
  },
  { deep: true }
);

// 方法
// 获取字典选项
async function fetchDictOptions() {
  try {
    // 获取收款科目选项
    const subjectRes = await getDictOptions("receivable_subject");
    subjectOptions.value = (subjectRes.data || []).map((item) => ({
      label: item.option_label,
      value: item.option_value,
    }));

    // 注意：收款方式选项现在通过fetchOrgAccounts方法获取
  } catch (error) {
    console.error("获取字典选项失败:", error);
    messages.error("获取字典选项失败");
  }
}

// 获取机构可用收款账户
async function fetchOrgAccounts(orgId) {
  if (!orgId) {
    accountOptions.value = [];
    return;
  }

  try {
    // 调用accounts.js的API获取机构账户列表
    const response = await accountsApi.getAccountsList({
      ownerOrgId: orgId,
      page: 1,
      size: 100, // 设置较大的size以获取所有账户
    });

    if (response.code === 200 && response.data && response.data.list) {
      // 过滤出可收款且状态为可用的账户
      const availableAccounts = response.data.list.filter(
        (account) => account.receivable === true && account.usable === true
      );

      // 转换为下拉框选项格式
      accountOptions.value = availableAccounts.map((account) => ({
        label: account.abbr, // 账户名称作为显示标签
        value: account.id.toString(), // 账户ID作为值
        account: account, // 保存完整账户信息，以便需要时使用
      }));
    } else {
      accountOptions.value = [];
      console.warn("获取机构账户列表失败或返回数据为空");
    }
  } catch (error) {
    console.error("获取机构可用收款账户失败:", error);
    messages.error("获取机构可用收款账户失败");
    accountOptions.value = [];
  }
}

// 处理部门选择变化
async function handleDepartmentChange(department) {
  // 当部门选择变化时，更新值
  if (department) {
    receptForm.receptOrgId = department.id;
    selectedDepartment.value = department;

    // 清空之前选择的收款方式
    receptForm.receptMethod = null;

    // 获取该机构的可用收款账户
    await fetchOrgAccounts(department.id);
  } else {
    receptForm.receptOrgId = null;
    selectedDepartment.value = null;
    accountOptions.value = []; // 清空收款方式选项
  }
}

// 处理收款方式变化
function handleAccountChange(value) {
  // 当收款方式变化时，更新值
  receptForm.receptMethod = value;

  // 不再尝试手动验证，避免回调问题
  if (value) {
  }
}

// 金额变化现在完全由列表项自动计算，不再需要手动处理

// 显示应收账款选择器
function showReceivableSelector() {
  receivableSelectorVisible.value = true;
}

// 添加其他收款
function addOtherRecept() {
  // 创建一个新的空行，直接添加到表格中
  const newRow = {
    id: `other-${Date.now()}`, // 生成临时ID
    feeName: "", // 空的收款科目，等待用户编辑
    feeNameLabel: "", // 收款科目的中文标签
    feeAmount: 100, // 默认1元（100分）
    summary: "", // 空的收款摘要，等待用户编辑
    isOtherRecept: true, // 标记为其他收款
    isEditing: true, // 标记为编辑状态，用于控制行内编辑
  };

  // 添加到列表
  receivableItems.value.push(newRow);

  // 更新总金额
  updateTotalAmount();

  // 提示用户
  messages.success("已添加新行，请编辑收款信息");
}

// 处理应收账款选择（单选模式）
function handleReceivableSelected(receivable) {
  selectedReceivable.value = receivable;
  selectedReceivables.value = [];

  // 添加到应收账款列表
  addReceivableToList(receivable);

  // 通知父组件
  emit("update:receivable", receivable);

  messages.success(`已选择应收账款：${receivable.feeName}`);
}

// 添加应收账款到列表
function addReceivableToList(receivable) {
  // 检查是否已存在
  const exists = receivableItems.value.some(
    (item) => item.id === receivable.id
  );
  if (!exists) {
    // 添加isEditing属性，默认为false（非编辑状态）
    const newReceivable = {
      ...receivable,
      isEditing: false, // 默认不处于编辑状态
      isOtherRecept: false, // 标记为应收账款，非其他收款
    };
    receivableItems.value.push(newReceivable);
    // 更新总金额
    updateTotalAmount();
  }
}

// 处理应收账款多选
function handleReceivablesMultipleSelected(receivables) {
  selectedReceivables.value = receivables;
  selectedReceivable.value = null;

  if (receivables.length > 0) {
    // 将新选择的应收账款添加到列表中（不替换现有列表）
    for (const receivable of receivables) {
      addReceivableToList(receivable);
    }

    // 更新总金额
    updateTotalAmount();

    // 通知父组件
    emit("update:receivables", selectedReceivables.value);

    messages.success(
      `已选择 ${receivables.length} 条应收账款，总金额：¥${formatMoney(
        totalReceivableAmount.value
      )}`
    );
  } else {
    // 如果没有选择任何应收账款，不做任何操作
    // 保留现有列表
    messages.info("未选择任何应收账款");
  }
}

// 移除应收账款项
function removeReceivableItem(item) {
  const index = receivableItems.value.findIndex((i) => i.id === item.id);
  if (index !== -1) {
    receivableItems.value.splice(index, 1);

    // 如果是多选模式，更新selectedReceivables
    if (props.multiple) {
      selectedReceivables.value = selectedReceivables.value.filter(
        (i) => i.id !== item.id
      );
      emit("update:receivables", selectedReceivables.value);
    } else if (
      selectedReceivable.value &&
      selectedReceivable.value.id === item.id
    ) {
      selectedReceivable.value = null;
      emit("update:receivable", null);
    }

    // 不再需要更新关联ID

    // 更新总金额
    updateTotalAmount();

    messages.success("已移除应收账款项");
  }
}

// 处理应收账款选择取消
function handleReceivableSelectCancel() {
  messages.info("已取消选择应收账款");
}

// 编辑行
function editRow(row) {
  row.isEditing = true;
  messages.info("请编辑收款信息");
}

// 确认编辑行
function confirmEditRow(row) {
  // 验证输入
  if (!row.feeName) {
    messages.error("请选择收款科目");
    return;
  }
  if (!row.feeAmount || row.feeAmount < 1) {
    // 至少1分钱
    messages.error("请输入有效的收款金额");
    return;
  }

  // 如果是其他收款且没有设置feeNameLabel，根据feeName查找对应的标签
  if (row.isOtherRecept && !row.feeNameLabel && row.feeName) {
    const selectedOption = subjectOptions.value.find(
      (opt) => opt.value === row.feeName
    );
    if (selectedOption) {
      row.feeNameLabel = selectedOption.label;
    }
  }

  // 取消编辑状态
  row.isEditing = false;

  // 更新总金额
  updateTotalAmount();

  // 根据行类型显示不同的成功消息
  if (row.isOtherRecept) {
    messages.success("其他收款信息已保存");
  } else {
    messages.success("应收账款信息已更新");
  }
}

// 处理取消按钮
function handleCancel() {
  emit("cancel");
}

// 处理提交按钮
function handleSubmit() {
  submitting.value = true;
  validate()
    .then((formData) => {
      emit("submit", formData);
      submitting.value = false;
    })
    .catch((errors) => {
      console.error("表单验证错误:", errors);
      messages.error("请完善表单信息");
      submitting.value = false;
    });
}

// 验证表单并提交
function validate() {
  return new Promise((resolve, reject) => {
    // 检查是否有收款项
    if (receivableItems.value.length === 0) {
      messages.error("请至少添加一项收款");
      reject(new Error("请至少添加一项收款"));
      return;
    }

    // 检查是否有正在编辑的行
    const editingRow = receivableItems.value.find((item) => item.isEditing);
    if (editingRow) {
      messages.error("请先完成正在编辑的收款项");
      reject(new Error("请先完成正在编辑的收款项"));
      return;
    }

    // 检查所有行的有效性
    for (const item of receivableItems.value) {
      if (!item.feeName) {
        messages.error("收款科目不能为空");
        reject(new Error("收款科目不能为空"));
        return;
      }
      if (!item.feeAmount || item.feeAmount < 1) {
        messages.error("收款金额必须大于0");
        reject(new Error("收款金额必须大于0"));
        return;
      }
    }

    // 手动验证表单字段，避免使用回调
    let hasError = false;

    // 检查收款单位
    if (!receptForm.receptOrgId) {
      messages.error("请选择收款单位");
      hasError = true;
    }

    // 检查收款方式
    if (!receptForm.receptMethod) {
      messages.error("请选择收款方式");
      hasError = true;
    }

    // 检查收款金额
    if (!receptForm.receptAmount || receptForm.receptAmount < 0.01) {
      messages.error("请输入有效的收款金额");
      hasError = true;
    }

    // 检查收款时间
    if (!receptForm.receptTime) {
      messages.error("请选择收款时间");
      hasError = true;
    }

    if (hasError) {
      reject(new Error("表单验证失败"));
      return;
    }

    // 构造提交的数据
    const formData = {
      id: receptForm.id,
      receptOrgId: receptForm.receptOrgId,
      receptTime:
        typeof receptForm.receptTime === "number"
          ? new Date(receptForm.receptTime)
              .toISOString()
              .replace("T", " ")
              .substring(0, 19)
          : receptForm.receptTime,
      receptAmount: Math.round(receptForm.receptAmount * 100), // 将金额单位从元转换为分
      receptAmountCn: amountInChinese.value,
      receptMethod: receptForm.receptMethod,
      bizNo: receptForm.bizNo,
      receptSummary: receptForm.receptSummary,
      items: receivableItems.value.map((item) => ({
        // 如果是应收账款数据，使用应收账款组件返回的feeId；如果是其他收款，使用字典选项的option_value
        feeId: item.isOtherRecept ? item.feeName : item.feeId || item.feeName,
        feeAmount: item.feeAmount, // 已经是分为单位
        feeSummary: item.summary || "",
        receiveableId: item.id.toString().startsWith("other-") ? null : item.id,
      })),
    };

    resolve(formData);
  });
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm: () => {
    receptForm.id = null;
    receptForm.receptOrgId = 0;
    receptForm.receptMethod = null;
    receptForm.receptAmount = 0.01;
    receptForm.receptAmountCn = "";
    receptForm.receptTime = Date.now();
    receptForm.bizNo = "";
    receptForm.receptSummary = "";
    receptForm.items = [];
    selectedDepartment.value = null;
    selectedReceivable.value = null;
    selectedReceivables.value = [];
    receivableItems.value = [];
  },
});
</script>

<style scoped>
.recept-form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%; /* 填满父容器宽度 */
  height: 100%; /* 填满父容器高度 */
}

.form-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
  position: relative;
  padding-left: 12px;
}

.section-header h3::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.empty-tip {
  padding: 24px 0;
  text-align: center;
}

.total-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 16px;
  margin-top: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.total-amount {
  font-weight: 600;
  color: #f5222d;
  margin-left: 8px;
  font-size: 16px;
}

.form-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #f0f0f0;
}
</style>
