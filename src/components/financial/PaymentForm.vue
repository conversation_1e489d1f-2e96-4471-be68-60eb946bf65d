<template>
  <div class="payment-form-container">
    <!-- 付款单基本信息部分 -->
    <div class="form-section">
      <div class="section-header">
        <h3>基础信息</h3>
      </div>
      <n-form
        ref="paymentFormRef"
        :model="paymentForm"
        :rules="paymentRules"
        label-placement="top"
        require-mark-placement="right-hanging"
      >
        <n-grid :cols="4" :x-gap="16">
          <n-grid-item>
            <n-form-item label="付款单位" path="paymentOrgId">
              <department-selector
                v-model="selectedDepartment"
                mode="single"
                label="请选择付款单位"
                @update:model-value="handleDepartmentChange"
                width="100%"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="付款方式" path="paymentMethod">
              <n-select
                v-model:value="paymentForm.paymentMethod"
                :options="accountOptions"
                placeholder="请选择付款方式"
                @update:value="handleAccountChange"
                :status="paymentForm.paymentMethod ? 'success' : undefined"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="付款时间" path="paymentTime">
              <n-date-picker
                v-model:value="paymentForm.paymentTime"
                type="datetime"
                clearable
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="付款金额（元）" path="paymentAmount">
              <n-input-number
                v-model:value="paymentForm.paymentAmount"
                :min="0.01"
                :precision="2"
                button-placement="both"
                :step="1000"
                style="width: 100%"
                :default-value="0.01"
                :validator="(value) => value >= 0.01"
                disabled
              >
                <template #prefix> ¥ </template>
              </n-input-number>
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="付款金额（大写）">
              <n-input v-model:value="amountInChinese" disabled />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="业务流水号">
              <n-input
                v-model:value="paymentForm.bizNo"
                placeholder="请输入业务流水号"
                maxlength="20"
                clearable
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="付款摘要" path="paymentSummary">
              <n-input
                v-model:value="paymentForm.paymentSummary"
                placeholder="请输入付款摘要"
                maxlength="100"
                clearable
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </n-form>
    </div>

    <!-- 付款明细数据列表部分 -->
    <div class="form-section">
      <div class="section-header">
        <h3>付款明细</h3>
        <div class="button-group">
          <n-button type="primary" size="small" @click="showPayableSelector">
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            应付账款
          </n-button>
          <n-button type="info" size="small" @click="addOtherPayment">
            <template #icon>
              <n-icon><AddOutline /></n-icon>
            </template>
            其他付款
          </n-button>
        </div>
      </div>

      <!-- 付款明细数据表格 -->
      <n-data-table
        :columns="paymentDetailColumns"
        :data="payableItems"
        :bordered="false"
        :single-line="false"
        :pagination="false"
        size="small"
      >
        <template #empty>
          <n-empty description="请选择应付账款或手动添加付款明细" />
        </template>
      </n-data-table>

      <!-- 合计行 -->
      <div v-if="payableItems.length > 0" class="total-row">
        <span>合计：</span>
        <span class="total-amount">¥{{ formatMoney(totalPayableAmount) }}</span>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="form-footer">
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button type="primary" @click="handleSubmit" :loading="submitting"
          >保存</n-button
        >
      </n-space>
    </div>
  </div>

  <!-- 应付账款选择器 -->
  <payable-selector
    v-model:visible="payableSelectorVisible"
    :multiple="multiple"
    :initial-selected="selectedPayables"
    @select="handlePayableSelected"
    @selectMultiple="handlePayablesMultipleSelected"
    @cancel="handlePayableSelectCancel"
  />
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, h } from "vue";
import {
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NInputNumber,
  NButton,
  NGrid,
  NGridItem,
  NDataTable,
  NSpace,
  NIcon,
  NEmpty,
  NDatePicker,
} from "naive-ui";
import {
  AddOutline,
  TrashOutline,
  CheckmarkOutline,
  PencilOutline,
} from "@vicons/ionicons5";
import { getDictOptions } from "@/api/dict";
import { convertNumberToChinese } from "@/utils/money";
import messages from "@/utils/messages";
import PayableSelector from "@/components/financial/PayableSelector.vue";
import DepartmentSelector from "@/components/users/DepartmentSelector.vue";
import accountsApi from "@/api/accounts";

// 定义组件属性
const props = defineProps({
  // 初始表单数据
  initialData: {
    type: Object,
    default: () => ({
      id: null,
      paymentOrgId: 0,
      paymentMethod: null, // 付款方式
      paymentAmount: 0.01, // 默认值设为0.01，单位是元
      paymentAmountCn: "", // 付款金额大写
      paymentTime: null, // 付款时间
      bizNo: "", // 业务流水号，选填
      paymentSummary: "", // 付款摘要
      items: [], // 付款明细项
    }),
  },
  // 是否编辑模式
  isEdit: {
    type: Boolean,
    default: false,
  },
  // 是否允许多选应付账款
  multiple: {
    type: Boolean,
    default: true,
  },
  // 初始选中的应付账款（单选模式）
  initialPayable: {
    type: Object,
    default: null,
  },
  // 初始选中的应付账款列表（多选模式）
  initialPayables: {
    type: Array,
    default: () => [],
  },
});

// 定义组件事件
const emit = defineEmits([
  "submit", // 提交表单
  "cancel", // 取消操作
  "update:payable", // 更新选中的应付账款（单选模式）
  "update:payables", // 更新选中的应付账款列表（多选模式）
]);

// 表单引用
const paymentFormRef = ref(null);
const submitting = ref(false);

// 表单数据
const paymentForm = reactive({
  id: props.initialData.id,
  paymentOrgId: props.initialData.paymentOrgId || 0,
  paymentMethod: props.initialData.paymentMethod || null, // 付款方式（原paymentAccount）
  paymentAmount: props.initialData.paymentAmount || 0.01, // 默认值设为0.01，单位是元
  paymentAmountCn: props.initialData.paymentAmountCn || "", // 付款金额大写
  paymentTime: props.initialData.paymentTime || Date.now(), // 默认为当前时间戳
  bizNo: props.initialData.bizNo || "", // 业务流水号，选填
  paymentSummary: props.initialData.paymentSummary || "", // 付款摘要
  items: [], // 付款明细项
});

// 应付账款选择器相关
const payableSelectorVisible = ref(false);
const selectedPayable = ref(props.initialPayable);
const selectedPayables = ref(props.initialPayables || []);

// 部门选择器相关
const selectedDepartment = ref(null);

// 应付账款数据项
const payableItems = ref([]);

// 付款账户选项
const accountOptions = ref([]);
// 付款科目选项
const subjectOptions = ref([]);

// 付款金额大写
const amountInChinese = computed(() => {
  if (!paymentForm.paymentAmount || paymentForm.paymentAmount <= 0) {
    return "零元整";
  }
  // 输入框中是元为单位，直接传递给转换函数
  return convertNumberToChinese(paymentForm.paymentAmount);
});

// 应付账款总金额
const totalPayableAmount = computed(() => {
  return (
    payableItems.value.reduce((sum, item) => sum + (item.feeAmount || 0), 0) /
    100
  ); // 分转元
});

// 格式化金额显示
function formatMoney(amount) {
  if (amount === undefined || amount === null) return "0.00";
  return amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

// 付款明细表格列定义
const paymentDetailColumns = [
  {
    title: "付款科目",
    key: "feeName",
    width: 200,
    render(row) {
      // 如果是其他付款且处于编辑状态，显示下拉选择框
      if (row.isOtherPayment && row.isEditing) {
        return h(NSelect, {
          value: row.feeName,
          options: subjectOptions.value,
          placeholder: "请选择付款科目",
          clearable: false,
          style: { width: "100%" },
          onUpdateValue: (value) => {
            row.feeName = value;
            // 保存选中的科目标签，用于显示
            const selectedOption = subjectOptions.value.find(
              (opt) => opt.value === value
            );
            if (selectedOption) {
              row.feeNameLabel = selectedOption.label;
            }
          },
        });
      }
      // 应付账款行不允许修改科目，即使处于编辑状态
      // 否则显示文本
      // 如果有feeNameLabel（保存的中文名称），优先显示它
      return row.feeNameLabel || row.feeName;
    },
  },
  {
    title: "付款金额",
    key: "feeAmount",
    width: 150,
    render(row) {
      // 如果处于编辑状态，显示数字输入框（无论是应付账款还是其他付款）
      if (row.isEditing) {
        return h(NInputNumber, {
          value: row.feeAmount / 100, // 分转元
          min: 0.01,
          precision: 2,
          step: 1000,
          buttonPlacement: "both",
          style: { width: "100%" },
          onUpdateValue: (value) => {
            if (value) {
              row.feeAmount = Math.round(value * 100); // 元转分
              // 更新总金额
              updateTotalAmount();
            }
          },
        });
      }
      // 否则显示格式化的金额
      const amount = row.feeAmount || 0;
      return `￥${(amount / 100).toLocaleString("zh-CN", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })}`;
    },
  },
  {
    title: "付款摘要",
    key: "summary",
    width: 200,
    render(row) {
      // 如果处于编辑状态，显示文本输入框（无论是应付账款还是其他付款）
      if (row.isEditing) {
        return h(NInput, {
          value:
            row.summary ||
            (row.orderSn ? `${row.customerName}(${row.mobile})` : ""),
          placeholder: "请输入付款摘要",
          onUpdateValue: (value) => {
            row.summary = value;
          },
        });
      }

      // 如果是应付账款且有自定义摘要，优先显示自定义摘要
      if (row.orderSn) {
        return row.summary || `${row.customerName}(${row.mobile})`;
      }
      // 如果是其他付款，显示摘要
      return row.summary || "其他付款";
    },
  },
  {
    title: "操作",
    key: "actions",
    width: 120,
    align: "center",
    render(row) {
      const buttons = [];

      // 如果处于编辑状态，显示确认按钮（无论是应付账款还是其他付款）
      if (row.isEditing) {
        buttons.push(
          h(
            NButton,
            {
              size: "small",
              quaternary: true,
              type: "success",
              onClick: () => confirmEditRow(row),
            },
            {
              default: () =>
                h(NIcon, null, { default: () => h(CheckmarkOutline) }),
            }
          )
        );
      }

      // 如果不处于编辑状态，显示编辑按钮（无论是应付账款还是其他付款）
      if (!row.isEditing) {
        buttons.push(
          h(
            NButton,
            {
              size: "small",
              quaternary: true,
              type: "info",
              onClick: () => editRow(row),
            },
            {
              default: () =>
                h(NIcon, null, { default: () => h(PencilOutline) }),
            }
          )
        );
      }

      // 所有行都显示删除按钮
      buttons.push(
        h(
          NButton,
          {
            size: "small",
            quaternary: true,
            type: "error",
            onClick: () => removePayableItem(row),
          },
          {
            default: () => h(NIcon, null, { default: () => h(TrashOutline) }),
          }
        )
      );

      return h(NSpace, { size: "small" }, { default: () => buttons });
    },
  },
];

// 表单验证规则
const paymentRules = {
  paymentOrgId: {
    required: true,
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择付款单位");
      }
      return true;
    },
    trigger: ["blur", "change", "input", "update"],
  },
  paymentMethod: {
    required: true,
    validator: (_, value) => {
      if (!value) {
        return new Error("请选择付款方式");
      }
      return true;
    },
    trigger: ["blur", "change", "input", "update"],
  },
  paymentAmount: {
    required: true,
    validator: (_, value) => {
      if (value === null || value === undefined || value === "") {
        return new Error("请输入付款金额");
      }
      if (typeof value === "number" && value < 0.01) {
        return new Error("付款金额必须大于0");
      }
      return true;
    },
    trigger: ["blur", "change", "input"],
  },
  paymentTime: {
    required: true,
    message: "请选择付款时间",
    trigger: ["blur", "change"],
  },
  paymentSummary: {
    required: false,
    trigger: ["blur", "change"],
  },
};

// 生命周期钩子
onMounted(async () => {
  await fetchDictOptions();

  // 如果是编辑模式，初始化表单数据
  if (props.isEdit && props.initialData) {
    Object.assign(paymentForm, props.initialData);

    // 如果有付款单位ID，初始化selectedDepartment
    if (paymentForm.paymentOrgId) {
      selectedDepartment.value = {
        id: paymentForm.paymentOrgId,
        name: "已选择的部门", // 这里可能需要通过API获取部门名称
      };

      // 获取该机构的可用付款账户
      await fetchOrgAccounts(paymentForm.paymentOrgId);
    }
  }

  // 初始化应付账款数据项
  initPayableItems();
});

// 初始化应付账款数据项
function initPayableItems() {
  // 不再清空现有列表
  // payableItems.value = [];

  // 如果有初始选中的应付账款，添加到列表
  if (
    props.multiple &&
    props.initialPayables &&
    props.initialPayables.length > 0
  ) {
    // 使用循环添加每一项，避免重复
    for (const payable of props.initialPayables) {
      addPayableToList(payable);
    }
  } else if (props.initialPayable) {
    // 添加单个应付账款
    addPayableToList(props.initialPayable);
  }

  // 更新总金额
  updateTotalAmount();
}

// 更新总金额
function updateTotalAmount() {
  if (payableItems.value.length > 0) {
    const totalAmount = payableItems.value.reduce(
      (sum, item) => sum + (item.feeAmount || 0),
      0
    );
    paymentForm.paymentAmount = totalAmount / 100; // 将分转换为元
  } else {
    paymentForm.paymentAmount = 0.01;
  }
}

// 监听 props 变化
watch(
  () => props.initialData,
  (newVal) => {
    if (newVal) {
      Object.assign(paymentForm, newVal);
    }
  },
  { deep: true }
);

watch(
  () => props.initialPayable,
  (newVal) => {
    selectedPayable.value = newVal;
    if (newVal) {
      // 添加到应付账款列表
      addPayableToList(newVal);
    }
  },
  { deep: true }
);

watch(
  () => props.initialPayables,
  (newVal) => {
    selectedPayables.value = newVal || [];
    if (newVal && newVal.length > 0) {
      // 将新的应付账款添加到列表中，而不是初始化整个列表
      for (const payable of newVal) {
        addPayableToList(payable);
      }

      // 更新总金额
      updateTotalAmount();
    }
  },
  { deep: true }
);

// 方法
// 获取字典选项
async function fetchDictOptions() {
  try {
    // 获取付款科目选项
    const subjectRes = await getDictOptions("payable_subject");
    subjectOptions.value = (subjectRes.data || []).map((item) => ({
      label: item.option_label,
      value: item.option_value,
    }));

    // 注意：付款方式选项现在通过fetchOrgAccounts方法获取
  } catch (error) {
    console.error("获取字典选项失败:", error);
    messages.error("获取字典选项失败");
  }
}

// 获取机构可用付款账户
async function fetchOrgAccounts(orgId) {
  if (!orgId) {
    accountOptions.value = [];
    return;
  }

  try {
    // 调用accounts.js的API获取机构账户列表
    const response = await accountsApi.getAccountsList({
      ownerOrgId: orgId,
      page: 1,
      size: 100, // 设置较大的size以获取所有账户
    });

    if (response.code === 200 && response.data && response.data.list) {
      // 过滤出可付款且状态为可用的账户
      const availableAccounts = response.data.list.filter(
        (account) => account.payable === true && account.usable === true
      );

      // 转换为下拉框选项格式
      accountOptions.value = availableAccounts.map((account) => ({
        label: account.abbr, // 账户名称作为显示标签
        value: account.id.toString(), // 账户ID作为值
        account: account, // 保存完整账户信息，以便需要时使用
      }));
    } else {
      accountOptions.value = [];
      console.warn("获取机构账户列表失败或返回数据为空");
    }
  } catch (error) {
    console.error("获取机构可用付款账户失败:", error);
    messages.error("获取机构可用付款账户失败");
    accountOptions.value = [];
  }
}

// 处理部门选择变化
async function handleDepartmentChange(department) {
  // 当部门选择变化时，更新值
  if (department) {
    paymentForm.paymentOrgId = department.id;
    selectedDepartment.value = department;

    // 清空之前选择的付款方式
    paymentForm.paymentMethod = null;

    // 获取该机构的可用付款账户
    await fetchOrgAccounts(department.id);
  } else {
    paymentForm.paymentOrgId = null;
    selectedDepartment.value = null;
    accountOptions.value = []; // 清空付款方式选项
  }
}

// 处理付款方式变化
function handleAccountChange(value) {
  // 当付款方式变化时，更新值
  paymentForm.paymentMethod = value;

  // 不再尝试手动验证，避免回调问题
  if (value) {
  }
}

// 金额变化现在完全由列表项自动计算，不再需要手动处理

// 显示应付账款选择器
function showPayableSelector() {
  payableSelectorVisible.value = true;
}

// 添加其他付款
function addOtherPayment() {
  // 创建一个新的空行，直接添加到表格中
  const newRow = {
    id: `other-${Date.now()}`, // 生成临时ID
    feeName: "", // 空的付款科目，等待用户编辑
    feeNameLabel: "", // 付款科目的中文标签
    feeAmount: 100, // 默认1元（100分）
    summary: "", // 空的付款摘要，等待用户编辑
    isOtherPayment: true, // 标记为其他付款
    isEditing: true, // 标记为编辑状态，用于控制行内编辑
  };

  // 添加到列表
  payableItems.value.push(newRow);

  // 更新总金额
  updateTotalAmount();

  // 提示用户
  messages.success("已添加新行，请编辑付款信息");
}

// 处理应付账款选择（单选模式）
function handlePayableSelected(payable) {
  selectedPayable.value = payable;
  selectedPayables.value = [];

  // 添加到应付账款列表
  addPayableToList(payable);

  // 通知父组件
  emit("update:payable", payable);

  messages.success(`已选择应付账款：${payable.feeName}`);
}

// 添加应付账款到列表
function addPayableToList(payable) {
  // 检查是否已存在
  const exists = payableItems.value.some((item) => item.id === payable.id);
  if (!exists) {
    // 添加isEditing属性，默认为false（非编辑状态）
    const newPayable = {
      ...payable,
      isEditing: false, // 默认不处于编辑状态
      isOtherPayment: false, // 标记为应付账款，非其他付款
    };
    payableItems.value.push(newPayable);
    // 更新总金额
    updateTotalAmount();
  }
}

// 处理应付账款多选
function handlePayablesMultipleSelected(payables) {
  selectedPayables.value = payables;
  selectedPayable.value = null;

  if (payables.length > 0) {
    // 将新选择的应付账款添加到列表中（不替换现有列表）
    for (const payable of payables) {
      addPayableToList(payable);
    }

    // 更新总金额
    updateTotalAmount();

    // 通知父组件
    emit("update:payables", selectedPayables.value);

    messages.success(
      `已选择 ${payables.length} 条应付账款，总金额：¥${formatMoney(
        totalPayableAmount.value
      )}`
    );
  } else {
    // 如果没有选择任何应付账款，不做任何操作
    // 保留现有列表
    messages.info("未选择任何应付账款");
  }
}

// 移除应付账款项
function removePayableItem(item) {
  const index = payableItems.value.findIndex((i) => i.id === item.id);
  if (index !== -1) {
    payableItems.value.splice(index, 1);

    // 如果是多选模式，更新selectedPayables
    if (props.multiple) {
      selectedPayables.value = selectedPayables.value.filter(
        (i) => i.id !== item.id
      );
      emit("update:payables", selectedPayables.value);
    } else if (selectedPayable.value && selectedPayable.value.id === item.id) {
      selectedPayable.value = null;
      emit("update:payable", null);
    }

    // 不再需要更新关联ID

    // 更新总金额
    updateTotalAmount();

    messages.success("已移除应付账款项");
  }
}

// 处理应付账款选择取消
function handlePayableSelectCancel() {
  messages.info("已取消选择应付账款");
}

// 编辑行
function editRow(row) {
  row.isEditing = true;
  messages.info("请编辑付款信息");
}

// 确认编辑行
function confirmEditRow(row) {
  // 验证输入
  if (!row.feeName) {
    messages.error("请选择付款科目");
    return;
  }
  if (!row.feeAmount || row.feeAmount < 1) {
    // 至少1分钱
    messages.error("请输入有效的付款金额");
    return;
  }

  // 如果是其他付款且没有设置feeNameLabel，根据feeName查找对应的标签
  if (row.isOtherPayment && !row.feeNameLabel && row.feeName) {
    const selectedOption = subjectOptions.value.find(
      (opt) => opt.value === row.feeName
    );
    if (selectedOption) {
      row.feeNameLabel = selectedOption.label;
    }
  }

  // 取消编辑状态
  row.isEditing = false;

  // 更新总金额
  updateTotalAmount();

  // 根据行类型显示不同的成功消息
  if (row.isOtherPayment) {
    messages.success("其他付款信息已保存");
  } else {
    messages.success("应付账款信息已更新");
  }
}

// 处理取消按钮
function handleCancel() {
  emit("cancel");
}

// 处理提交按钮
function handleSubmit() {
  submitting.value = true;
  validate()
    .then((formData) => {
      emit("submit", formData);
      submitting.value = false;
    })
    .catch((errors) => {
      console.error("表单验证错误:", errors);
      messages.error("请完善表单信息");
      submitting.value = false;
    });
}

// 验证表单并提交
function validate() {
  return new Promise((resolve, reject) => {
    // 检查是否有付款项
    if (payableItems.value.length === 0) {
      messages.error("请至少添加一项付款");
      reject(new Error("请至少添加一项付款"));
      return;
    }

    // 检查是否有正在编辑的行
    const editingRow = payableItems.value.find((item) => item.isEditing);
    if (editingRow) {
      messages.error("请先完成正在编辑的付款项");
      reject(new Error("请先完成正在编辑的付款项"));
      return;
    }

    // 检查所有行的有效性
    for (const item of payableItems.value) {
      if (!item.feeName) {
        messages.error("付款科目不能为空");
        reject(new Error("付款科目不能为空"));
        return;
      }
      if (!item.feeAmount || item.feeAmount < 1) {
        messages.error("付款金额必须大于0");
        reject(new Error("付款金额必须大于0"));
        return;
      }
    }

    // 手动验证表单字段，避免使用回调
    let hasError = false;

    // 检查付款单位
    if (!paymentForm.paymentOrgId) {
      messages.error("请选择付款单位");
      hasError = true;
    }

    // 检查付款方式
    if (!paymentForm.paymentMethod) {
      messages.error("请选择付款方式");
      hasError = true;
    }

    // 检查付款金额
    if (!paymentForm.paymentAmount || paymentForm.paymentAmount < 0.01) {
      messages.error("请输入有效的付款金额");
      hasError = true;
    }

    // 检查付款时间
    if (!paymentForm.paymentTime) {
      messages.error("请选择付款时间");
      hasError = true;
    }

    if (hasError) {
      reject(new Error("表单验证失败"));
      return;
    }

    // 构造提交的数据
    const formData = {
      id: paymentForm.id,
      paymentOrgId: paymentForm.paymentOrgId,
      paymentTime:
        typeof paymentForm.paymentTime === "number"
          ? new Date(paymentForm.paymentTime)
              .toISOString()
              .replace("T", " ")
              .substring(0, 19)
          : paymentForm.paymentTime,
      paymentAmount: Math.round(paymentForm.paymentAmount * 100), // 将金额单位从元转换为分
      paymentAmountCn: amountInChinese.value,
      paymentMethod: paymentForm.paymentMethod,
      bizNo: paymentForm.bizNo,
      paymentSummary: paymentForm.paymentSummary,
      items: payableItems.value.map((item) => ({
        // 如果是应付账款数据，使用应付账款组件返回的feeId；如果是其他付款，使用字典选项的option_value
        feeId: item.isOtherPayment ? item.feeName : item.feeId || item.feeName,
        feeAmount: item.feeAmount, // 已经是分为单位
        feeSummary: item.summary || "",
        payableId: item.id.toString().startsWith("other-") ? null : item.id,
      })),
    };

    resolve(formData);
  });
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetForm: () => {
    paymentForm.id = null;
    paymentForm.paymentOrgId = 0;
    paymentForm.paymentMethod = null;
    paymentForm.paymentAmount = 0.01;
    paymentForm.paymentAmountCn = "";
    paymentForm.paymentTime = Date.now();
    paymentForm.bizNo = "";
    paymentForm.paymentSummary = "";
    paymentForm.items = [];
    selectedDepartment.value = null;
    selectedPayable.value = null;
    selectedPayables.value = [];
    payableItems.value = [];
  },
});
</script>

<style scoped>
.payment-form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%; /* 填满父容器宽度 */
  height: 100%; /* 填满父容器高度 */
}

.form-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
  position: relative;
  padding-left: 12px;
}

.section-header h3::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.empty-tip {
  padding: 24px 0;
  text-align: center;
}

.total-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 16px;
  margin-top: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.total-amount {
  font-weight: 600;
  color: #f5222d;
  margin-left: 8px;
  font-size: 16px;
}

.form-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #f0f0f0;
}
</style>
