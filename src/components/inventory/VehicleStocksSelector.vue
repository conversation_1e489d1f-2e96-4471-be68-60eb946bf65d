<template>
  <n-modal
    v-model:show="modelVisible"
    class="vehicle-stocks-selector"
    preset="card"
    title="车辆库存查询"
    :style="isMaximized ? { width: '1200px' } : { width: '800px' }"
    :mask-closable="false"
    transform-origin="center"
  >
    <template #header-extra>
      <n-button quaternary circle @click="toggleMaximized">
        <template #icon>
          <n-icon>
            <component
              :is="isMaximized ? ContractOutlineIcon : ExpandOutlineIcon"
            />
          </n-icon>
        </template>
      </n-button>
    </template>

    <!-- 搜索区域 -->
    <div class="search-area">
      <n-input
        v-model:value="searchKeywords"
        placeholder="请输入VIN或车型关键字进行搜索"
        clearable
        @keydown.enter="handleSearch"
        :style="{ width: '320px' }"
        :disabled="!!props.initialKeyword && props.disableSearch"
      >
        <template #prefix>
          <n-icon><component :is="SearchOutlineIcon" /></n-icon>
        </template>
        <template #suffix>
          <n-button
            type="primary"
            text
            @click="handleSearch"
            :loading="loading"
            :disabled="!!props.initialKeyword && props.disableSearch"
          >
            搜索
          </n-button>
        </template>
      </n-input>

      <div v-if="!props.multiple" class="selection-tip">
        <n-text type="info">单选模式：点击VIN选中车辆</n-text>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="stocksData"
        :loading="loading"
        :row-key="(row) => row.id || row.vin"
        :row-class-name="getRowClass"
        :pagination="false"
        :striped="true"
        :bordered="true"
        :scroll-x="1200"
        style="min-height: 300px"
        @update:checked-row-keys="handleSelectionChange"
        @update:checked-row="handleSelectionChange"
        @row-click="handleRowClick"
        @row-dblclick="handleRowDblClick"
      >
        <template #empty>
          <n-empty description="暂无数据" />
        </template>
      </n-data-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <n-pagination
        v-model:page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-sizes="pagination.pageSizes"
        :item-count="pagination.itemCount"
        :show-size-picker="pagination.showSizePicker"
        :show-quick-jumper="pagination.showQuickJumper"
        size-picker-option-text="条/页"
        page-size-option="每页"
        :prefix="() => `共 ${pagination.itemCount} 条`"
        :display-order="['prefix', 'pages', 'size-picker', 'quick-jumper']"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          :disabled="!selectedRows.length"
          @click="handleConfirm"
        >
          {{
            props.multiple
              ? `确定选择 (${selectedRows.length})`
              : selectedRows.length
              ? "确定选择"
              : "请双击选择一行"
          }}
        </n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, h, nextTick, markRaw } from "vue";
import { NIcon } from "naive-ui";
import {
  SearchOutline,
  ContractOutline,
  ExpandOutline,
} from "@vicons/ionicons5";

import stocksApi from "@/api/stocks";
import messages from "@/utils/messages";

// 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
const SearchOutlineIcon = markRaw(SearchOutline);
const ContractOutlineIcon = markRaw(ContractOutline);
const ExpandOutlineIcon = markRaw(ExpandOutline);

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  defaultSelected: {
    type: Array,
    default: () => [],
  },
  // 库存状态过滤
  stock_status: {
    type: String,
    default: "",
  },
  // 排序字段
  order_by: {
    type: String,
    default: "",
  },
  // 可选的过滤条件
  filters: {
    type: Object,
    default: () => ({}),
  },
  // 初始搜索关键词
  initialKeyword: {
    type: String,
    default: "",
  },
  // 是否禁用搜索框
  disableSearch: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits(["update:visible", "cancel", "confirm"]);

// 状态变量
const tableRef = ref(null);
const loading = ref(false);
const searchKeywords = ref("");
const stocksData = ref([]);
const selectedRows = ref([]);
const selectedRowKeys = ref([]);
// 默认最大化显示
const isMaximized = ref(true);

// 计算属性：模态框可见性
const modelVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 1,
  showSizePicker: false,
  pageSizes: [10, 20, 50],
  itemCount: 0,
  showQuickJumper: false,
});

// 切换最大化/最小化
const toggleMaximized = () => {
  isMaximized.value = !isMaximized.value;
};

// 表格列配置
const columns = computed(() => {
  const baseColumns = [];

  // 根据multiple属性决定是否添加selection列
  if (props.multiple) {
    baseColumns.push({
      type: "selection",
      width: 50,
      fixed: "left",
      align: "center",
    });
  }
  // 单选模式下不显示选择框，只能通过双击行选择

  return [
    ...baseColumns,
    {
      title: "VIN",
      key: "vin",
      width: 200, // 增加宽度以适应17位字符
      fixed: "left",
      align: "left",
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return h(
          "div",
          {
            style: {
              fontWeight: "bold",
              color: "#1890ff",
              fontFamily: "monospace", // 使用等宽字体更好地显示VIN
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
            },
            onClick: (e) => {
              e.stopPropagation(); // 阻止事件冒泡，避免触发行点击事件

              // 复制VIN到剪贴板
              if (row.vin) {
                navigator.clipboard
                  .writeText(row.vin)
                  .then(() => {
                    messages.success("VIN已复制到剪贴板");

                    // 选中该行
                    const rowKey = row.id || row.vin;
                    selectedRowKeys.value = [rowKey];
                    selectedRows.value = [row];

                    // 如果是单选模式，直接确认选择
                    if (!props.multiple) {
                      handleConfirm();
                    } else {
                      // 多选模式下更新表格选中状态
                      nextTick(() => {
                        if (
                          tableRef.value &&
                          typeof tableRef.value.clearChecked === "function" &&
                          typeof tableRef.value.check === "function"
                        ) {
                          tableRef.value.clearChecked();
                          tableRef.value.check(rowKey);
                        }
                      });
                    }
                  })
                  .catch((err) => {
                    console.error("复制失败:", err);
                    messages.error("复制失败，请手动复制");
                  });
              }
            },
          },
          [
            // VIN文本
            h("span", {}, row.vin || ""),
          ]
        );
      },
    },
    {
      title: "品牌",
      key: "brand",
      width: 120,
      align: "left",
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "车型",
      key: "series",
      width: 150,
      align: "left",
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "配置",
      key: "configName",
      width: 180,
      align: "left",
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "库龄(天)",
      key: "stockDays",
      width: 90,
      align: "center",
    },
    {
      title: "所在仓库",
      key: "stockOrgName",
      width: 180,
      align: "left",
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "所属单位",
      key: "ownerOrgName",
      width: 180,
      align: "left",
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: "启票金额（元）",
      key: "stockAmount",
      width: 120,
      align: "right",
    },
  ];
});

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 弹窗显示时，初始化数据
      initData();
    }
  }
);

// 监听stock_status变化
watch(
  () => props.stock_status,
  (newVal, oldVal) => {
    if (props.visible && newVal !== oldVal) {
      loadData();
    }
  }
);

// 监听order_by变化
watch(
  () => props.order_by,
  (newVal, oldVal) => {
    if (props.visible && newVal !== oldVal) {
      loadData();
    }
  }
);

// 组件挂载时，如果visible为true，则初始化数据
if (props.visible) {
  nextTick(() => {
    initData();
  });
}

// 初始化数据
const initData = () => {
  // 重置分页
  pagination.page = 1;

  // 设置搜索关键词，如果有初始关键词则使用，否则重置
  // 确保将initialKeyword转换为字符串类型
  if (props.initialKeyword) {
    searchKeywords.value = String(props.initialKeyword);
  } else {
    searchKeywords.value = "";
  }

  // 加载数据（此时会自动考虑stock_status和order_by参数）
  loadData();

  // 设置默认选中
  if (props.defaultSelected && props.defaultSelected.length) {
    selectedRowKeys.value = props.defaultSelected.map((item) => item.id);
    selectedRows.value = props.defaultSelected;
    nextTick(() => {
      // 使用正确的方法名称
      if (tableRef.value) {
        // 尝试不同的可能方法名
        if (
          typeof tableRef.value.clearChecked === "function" &&
          typeof tableRef.value.check === "function"
        ) {
          // 先清除所有选中
          tableRef.value.clearChecked();
          // 然后选中需要的行
          selectedRowKeys.value.forEach((key) => {
            tableRef.value.check(key);
          });
        } else {
          console.warn(
            "无法找到表格的check/clearChecked方法，请检查NaiveUI版本"
          );
        }
      }
    });
  } else {
    selectedRowKeys.value = [];
    selectedRows.value = [];
  }
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      page: pagination.page,
      size: pagination.pageSize,
    };

    // 添加搜索参数
    if (searchKeywords.value) {
      const trimmedKeyword = searchKeywords.value.trim();

      // 如果是初始关键词且禁用了搜索，则使用skuId参数
      if (
        props.initialKeyword &&
        props.disableSearch &&
        trimmedKeyword === String(props.initialKeyword)
      ) {
        params.skuId = trimmedKeyword;
      }
      // 如果关键词看起来像VIN码（17位字母数字组合），则专门设置vin参数
      else if (/^[A-HJ-NPR-Z0-9]{17}$/i.test(trimmedKeyword)) {
        params.vin = trimmedKeyword.toUpperCase();
      }
      // 否则使用普通关键词搜索
      else {
        params.keywords = trimmedKeyword;
      }
    }

    // 添加库存状态过滤
    if (props.stock_status) {
      params.stock_status = props.stock_status;
    }

    // 添加排序字段
    if (props.order_by) {
      params.order_by = props.order_by;
    }

    // 添加过滤条件
    if (props.filters) {
      Object.keys(props.filters).forEach((key) => {
        if (props.filters[key] !== null && props.filters[key] !== undefined) {
          params[key] = props.filters[key];
        }
      });
    }

    // 调用API获取数据
    const response = await stocksApi.getStocksList(params);

    if (response && response.code === 200) {
      // 确保response.data.list是数组
      if (response.data && Array.isArray(response.data.list)) {
        // 先清空数据
        stocksData.value = [];

        // 使用setTimeout确保DOM更新后再设置新数据
        setTimeout(() => {
          // 处理数据并赋值
          const processedData = response.data.list.map((item) => {
            // 如果需要，可以在这里对数据进行转换处理
            return {
              ...item,
              // 将金额从分转为元并格式化显示
              stockAmount: item.stockAmount
                ? "￥" + (item.stockAmount / 100).toLocaleString("zh-CN")
                : "未知",
              // 确保品牌、车型、配置字段有值
              brand: item.brand || item.vehicleBrand || "未知",
              model: item.model || item.vehicleModel || "未知",
              config:
                item.config || item.vehicleConfig || item.vehicleSpec || "标准",
            };
          });

          // 设置处理后的数据
          stocksData.value = processedData;
        }, 100);

        // 如果数据为空，显示提示
        if (response.data.list.length === 0) {
          messages.info("没有找到匹配的数据");
        }

        // 更新分页信息 - 适配返回的分页结构
        pagination.itemCount = response.data.total || 0;
        pagination.pageCount = response.data.pages || 1;

        // 确保当前页码与API返回的一致
        if (pagination.page !== response.data.pageNum) {
          pagination.page = response.data.pageNum || 1;
        }

        // 如果当前页大于总页数且总页数不为0，则跳转到最后一页
        if (pagination.page > response.data.pages && response.data.pages > 0) {
          pagination.page = response.data.pages;
          loadData();
          return;
        }
      } else {
        stocksData.value = [];
        messages.error("数据格式不正确");
        return;
      }
    } else {
      messages.error(response?.message || "数据加载失败");
      stocksData.value = [];
    }
  } catch (error) {
    messages.error("加载数据失败，请稍后重试");
    stocksData.value = [];
  } finally {
    // 延迟关闭加载状态，确保数据渲染完成
    setTimeout(() => {
      loading.value = false;
    }, 100);
  }
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  loadData();
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.page = page;
  loadData();
};

// 处理每页条数变化
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  loadData();
};

// 处理选择变化
const handleSelectionChange = (keys) => {
  console.log("选择变化:", keys, "单选模式:", !props.multiple);

  if (!props.multiple) {
    // 单选模式下，确保只有一个选中项
    if (keys.length > 0) {
      // 只保留最后选择的一项
      const lastKey = keys[keys.length - 1];
      selectedRowKeys.value = [lastKey];

      // 查找匹配的行，优先使用id，如果没有则使用vin
      const selectedRow = stocksData.value.find(
        (item) =>
          (item.id && item.id === lastKey) || (item.vin && item.vin === lastKey)
      );

      selectedRows.value = selectedRow ? [selectedRow] : [];

      // 如果选择了多个，强制只保留一个
      if (keys.length > 1) {
        // 确保表格UI反映正确的选中状态
        nextTick(() => {
          // 使用正确的方法名称
          if (tableRef.value) {
            // 尝试不同的可能方法名
            if (typeof tableRef.value.check === "function") {
              // 先清除所有选中
              tableRef.value.clearChecked();
              // 然后选中需要的行
              selectedRowKeys.value.forEach((key) => {
                tableRef.value.check(key);
              });
            } else {
              console.warn("无法找到表格的check方法，请检查NaiveUI版本");
            }
          }
        });
      }
    } else {
      selectedRowKeys.value = [];
      selectedRows.value = [];
    }
  } else {
    // 多选模式下，直接使用所有选中的键
    selectedRowKeys.value = keys;

    // 查找所有匹配的行
    selectedRows.value = stocksData.value.filter((item) => {
      return keys.some(
        (key) => (item.id && item.id === key) || (item.vin && item.vin === key)
      );
    });
  }
};

// 处理行点击
const handleRowClick = (row) => {
  // 获取行的唯一标识，优先使用id，如果没有则使用vin
  const rowKey = row.id || row.vin;

  if (!rowKey) {
    console.error("行数据缺少唯一标识:", row);
    return;
  }

  if (!props.multiple) {
    // 单选模式下，点击行高亮显示，但不选中复选框（因为没有复选框）
    selectedRowKeys.value = [rowKey];
    selectedRows.value = [row];
    // 不需要设置复选框状态，因为单选模式下没有复选框
  } else {
    // 多选模式下，点击行切换选中状态
    const index = selectedRowKeys.value.indexOf(rowKey);
    if (index > -1) {
      // 已选中，则取消选中
      selectedRowKeys.value.splice(index, 1);
      selectedRows.value = selectedRows.value.filter(
        (item) => item.id !== row.id && item.vin !== row.vin
      );
    } else {
      // 未选中，则添加到选中项
      selectedRowKeys.value.push(rowKey);
      selectedRows.value.push(row);
    }
    nextTick(() => {
      // 使用正确的方法名称
      if (tableRef.value) {
        // 尝试不同的可能方法名
        if (
          typeof tableRef.value.clearChecked === "function" &&
          typeof tableRef.value.check === "function"
        ) {
          // 先清除所有选中
          tableRef.value.clearChecked();
          // 然后选中需要的行
          selectedRowKeys.value.forEach((key) => {
            tableRef.value.check(key);
          });
        } else {
          console.warn(
            "无法找到表格的check/clearChecked方法，请检查NaiveUI版本"
          );
        }
      }
    });
  }
};

// 处理行双击
const handleRowDblClick = (row) => {
  console.log("双击行:", row);

  // 获取行的唯一标识，优先使用id，如果没有则使用vin
  const rowKey = row.id || row.vin;

  if (!rowKey) {
    console.error("行数据缺少唯一标识:", row);
    return;
  }

  if (props.multiple) {
    // 多选模式下，如果已经选中则取消选中，否则添加到选中项
    const index = selectedRowKeys.value.indexOf(rowKey);
    if (index > -1) {
      // 已选中，则取消选中
      selectedRowKeys.value.splice(index, 1);
      selectedRows.value = selectedRows.value.filter(
        (item) => item.id !== row.id && item.vin !== row.vin
      );
    } else {
      // 未选中，则添加到选中项
      selectedRowKeys.value.push(rowKey);
      selectedRows.value.push(row);
    }
    // 更新表格选中状态
    nextTick(() => {
      // 使用正确的方法名称
      if (tableRef.value) {
        // 尝试不同的可能方法名
        if (
          typeof tableRef.value.clearChecked === "function" &&
          typeof tableRef.value.check === "function"
        ) {
          // 先清除所有选中
          tableRef.value.clearChecked();
          // 然后选中需要的行
          selectedRowKeys.value.forEach((key) => {
            tableRef.value.check(key);
          });
        } else {
          console.warn(
            "无法找到表格的check/clearChecked方法，请检查NaiveUI版本"
          );
        }
      }
    });
  } else {
    // 单选模式下，直接选中并确认
    selectedRowKeys.value = [rowKey];
    selectedRows.value = [row];

    // 延迟一下再确认，让用户看到选中效果
    setTimeout(() => {
      // 单选模式下双击直接确认，不需要设置复选框状态
      handleConfirm();
    }, 100);
  }
};

// 获取行样式
const getRowClass = (row) => {
  // 获取行的唯一标识，优先使用id，如果没有则使用vin
  const rowKey = row.id || row.vin;

  // 检查是否在选中的行中
  return selectedRowKeys.value.includes(rowKey) ? "selected-row" : "";
};

// 处理取消
const handleCancel = () => {
  emit("update:visible", false);
  emit("cancel");
};

// 处理确认
const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    messages.warning("请至少选择一条记录");
    return;
  }

  // 关闭弹窗并触发确认事件
  emit("update:visible", false);
  emit("confirm", props.multiple ? selectedRows.value : selectedRows.value[0]);
};
</script>

<style scoped>
.vehicle-stocks-selector {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-modal__content) {
  display: flex;
  flex-direction: column;
}

:deep(.n-modal.n-modal--card-style .n-card__content) {
  flex: 1;
  overflow: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

:deep(.n-modal.n-modal--card-style .n-card__footer) {
  padding-top: 12px;
  padding-bottom: 12px;
}

.search-area {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.search-tips {
  margin-top: 4px;
  color: #999;
  font-size: 12px;
  text-align: left;
}

.selection-tip {
  margin-left: 16px;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(24, 144, 255, 0.1);
  font-size: 13px;
}

.table-container {
  flex: 1;
  overflow: visible;
  border: 1px solid #eee;
  border-radius: 4px;
  width: 100%;
  position: relative;
  min-height: 300px; /* 确保表格容器有最小高度 */
  display: flex;
  flex-direction: column;
}

:deep(.n-data-table) {
  flex: 1;
  height: 100%;
  min-height: 300px;
}

:deep(.n-data-table-base-table-header) {
  overflow: visible !important;
}

:deep(.n-data-table-base-table-body) {
  overflow: auto !important;
  min-height: 200px; /* 确保表格体有最小高度 */
}

:deep(.n-data-table-wrapper) {
  width: 100%;
}

:deep(.n-data-table .n-data-table-th.n-data-table-th--fixed-left),
:deep(.n-data-table .n-data-table-td.n-data-table-td--fixed-left) {
  background-color: #fff;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

:deep(.selected-row) {
  background-color: rgba(24, 160, 88, 0.1);
  border-left: 3px solid #18a058;
}

:deep(.n-data-table-tr:hover) {
  cursor: pointer;
  background-color: rgba(24, 160, 88, 0.1);
  transition: background-color 0.2s;
}

:deep(.n-data-table-tr:active) {
  background-color: rgba(24, 160, 88, 0.2);
}

:deep(.n-data-table-tr.n-data-table-tr--striped) {
  background-color: rgba(0, 0, 0, 0.02);
}

:deep(.n-data-table-tr.n-data-table-tr--striped.selected-row) {
  background-color: rgba(24, 160, 88, 0.1);
}
</style>
